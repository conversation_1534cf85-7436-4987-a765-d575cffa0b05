/* ===============================
   المتغيرات العامة والألوان
   =============================== */
:root {
    /* الألوان الأساسية المحسنة */
    --primary-color: #1e3a8a;
    --primary-light: #3b82f6;
    --primary-dark: #1e40af;
    --secondary-color: #0ea5e9;
    --secondary-light: #38bdf8;
    --secondary-dark: #0284c7;

    /* ألوان الحالة */
    --success-color: #10b981;
    --success-light: #34d399;
    --warning-color: #f59e0b;
    --warning-light: #fbbf24;
    --danger-color: #ef4444;
    --danger-light: #f87171;
    --info-color: #06b6d4;
    --info-light: #22d3ee;

    /* ألوان الخلفية والنص */
    --bg-color: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --text-color: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;

    /* ألوان الحدود والظلال */
    --border-color: #e2e8f0;
    --border-light: #f1f5f9;
    --border-dark: #cbd5e1;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* التأثيرات والانتقالات */
    --border-radius: 12px;
    --border-radius-sm: 8px;
    --border-radius-lg: 16px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);

    /* التدرجات */
    --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-light) 100%);
    --gradient-success: linear-gradient(135deg, var(--success-color) 0%, var(--success-light) 100%);
}

/* ===============================
   إعدادات عامة
   =============================== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--bg-color);
    color: var(--text-color);
    line-height: 1.6;
    direction: rtl;
    text-align: right;
}

.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* ===============================
   رأس الصفحة
   =============================== */
header {
    background: var(--gradient-primary);
    color: white;
    padding: 1.5rem 2rem;
    box-shadow: var(--shadow-lg);
    position: sticky;
    top: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

.logo {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo i {
    font-size: 2rem;
    color: var(--warning-color);
}

.logo h1 {
    font-size: 1.8rem;
    font-weight: bold;
}

.subtitle {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-right: 1rem;
}

.header-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.datetime {
    text-align: left;
    font-size: 0.9rem;
}

.dark-mode-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 0.5rem;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dark-mode-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* أزرار التحكم في الرأس */
.header-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.language-switcher {
    display: flex;
    gap: 5px;
}

.lang-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 6px 10px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 12px;
    font-weight: bold;
    min-width: 40px;
}

.lang-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

.lang-btn.active {
    background: rgba(16, 185, 129, 0.8);
    border-color: #10b981;
    color: white;
}

/* تنسيقات اللغة الفرنسية */
[dir="ltr"] {
    text-align: left;
}

[dir="ltr"] .sidebar {
    right: auto;
    left: 0;
}

[dir="ltr"] main {
    margin-right: 0;
    margin-left: 280px;
}

[dir="ltr"] .modal-content {
    text-align: left;
}

[dir="ltr"] .form-row {
    direction: ltr;
}

[dir="ltr"] .header-content {
    flex-direction: row;
}

[dir="ltr"] .header-info {
    text-align: right;
}

/* ===============================
   شريط التنقل
   =============================== */
nav {
    background: var(--light-color);
    border-bottom: 1px solid var(--border-color);
    padding: 0 2rem;
    overflow-x: auto;
}

nav ul {
    list-style: none;
    display: flex;
    max-width: 1200px;
    margin: 0 auto;
    gap: 0.5rem;
}

nav li {
    white-space: nowrap;
}

nav a {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 1.5rem;
    text-decoration: none;
    color: var(--text-color);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    transition: var(--transition);
    font-weight: 500;
    position: relative;
    overflow: hidden;
}

nav a::before {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 0;
    height: 3px;
    background: var(--gradient-secondary);
    transition: var(--transition);
}

nav a:hover::before {
    width: 100%;
}

nav a:hover {
    background: rgba(14, 165, 233, 0.1);
    color: var(--secondary-color);
    transform: translateY(-2px);
}

nav a.active {
    background: var(--gradient-secondary);
    color: white;
    box-shadow: 0 -4px 15px rgba(14, 165, 233, 0.3);
    transform: translateY(-2px);
}

nav a.active::before {
    width: 100%;
    background: rgba(255, 255, 255, 0.3);
}

nav a i {
    font-size: 1.1rem;
}

/* ===============================
   المحتوى الرئيسي
   =============================== */
main {
    flex: 1;
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
}

section {
    display: none;
    animation: fadeIn 0.5s;
}

section.active-section {
    display: block;
}

/* تحسين الرسوم المتحركة */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

/* تأثير التحميل */
.loading-shimmer {
    background: linear-gradient(90deg, var(--bg-secondary) 25%, var(--border-light) 50%, var(--bg-secondary) 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

section h2 {
    color: var(--primary-color);
    margin-bottom: 2rem;
    font-size: 2rem;
    border-bottom: 3px solid var(--secondary-color);
    padding-bottom: 0.5rem;
}

/* ===============================
   بطاقات لوحة التحكم
   =============================== */
.dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.card {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 1.5rem;
    transition: var(--transition);
    border: 1px solid var(--border-light);
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background: var(--gradient-secondary);
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--secondary-light);
}

.card:hover::before {
    width: 8px;
}

.card-icon {
    font-size: 3rem;
    color: var(--secondary-color);
    opacity: 0.8;
}

.card-info h3 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.card-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--secondary-color);
    margin: 0.5rem 0;
}

.card-info small {
    color: #7f8c8d;
    font-size: 0.9rem;
}

/* ===============================
   التنبيهات والإشعارات
   =============================== */
.alerts-section {
    margin-bottom: 3rem;
}

.alerts-section h3 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}

.alerts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.alert-card {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: var(--transition);
}

.alert-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.alert-icon {
    font-size: 2rem;
    color: var(--warning-color);
}

.alert-content h4 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.alert-content p {
    color: var(--warning-color);
    font-weight: bold;
    font-size: 1.1rem;
}

/* ===============================
   شريط الإجراءات
   =============================== */
.action-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.search-box {
    position: relative;
    min-width: 300px;
}

.search-box input {
    width: 100%;
    padding: 0.75rem 3rem 0.75rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
}

.search-box input:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.search-box i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--border-color);
    font-size: 1.1rem;
}

/* ===============================
   الأزرار
   =============================== */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.875rem 1.75rem;
    border: none;
    border-radius: var(--border-radius-sm);
    font-size: 0.95rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    white-space: nowrap;
    position: relative;
    overflow: hidden;
    letter-spacing: 0.025em;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.btn.primary {
    background: var(--gradient-secondary);
    color: white;
    border: 1px solid var(--secondary-dark);
}

.btn.primary:hover {
    background: var(--gradient-primary);
    box-shadow: 0 8px 25px rgba(14, 165, 233, 0.3);
}

.btn.secondary {
    background: var(--light-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.btn.secondary:hover {
    background: #d5dbdb;
}

.btn.success {
    background: var(--success-color);
    color: white;
}

.btn.success:hover {
    background: #229954;
}

.btn.warning {
    background: var(--warning-color);
    color: white;
}

.btn.warning:hover {
    background: #d68910;
}

.btn.danger {
    background: var(--danger-color);
    color: white;
}

.btn.danger:hover {
    background: #c0392b;
}

/* ===============================
   الجداول
   =============================== */
.table-container {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    margin-bottom: 2rem;
    border: 1px solid var(--border-light);
    transition: var(--transition);
}

.table-container:hover {
    box-shadow: var(--shadow-lg);
    border-color: var(--border-color);
}

table {
    width: 100%;
    border-collapse: collapse;
}

table th,
table td {
    padding: 1rem;
    text-align: right;
    border-bottom: 1px solid #ecf0f1;
}

table th {
    background: var(--light-color);
    color: var(--primary-color);
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 10;
}

table tr:hover {
    background: rgba(52, 152, 219, 0.05);
}

table tr:last-child td {
    border-bottom: none;
}

/* ===============================
   النوافذ المنبثقة
   =============================== */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(8px);
    animation: modalFadeIn 0.3s ease;
}

.modal-content {
    background: white;
    margin: 3% auto;
    padding: 2.5rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    width: 90%;
    max-width: 650px;
    max-height: 85vh;
    overflow-y: auto;
    position: relative;
    animation: modalSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid var(--border-light);
}

.modal-content.large {
    max-width: 950px;
}

.modal-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-60px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.close {
    position: absolute;
    left: 1rem;
    top: 1rem;
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    color: var(--border-color);
    transition: var(--transition);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.close:hover {
    color: var(--danger-color);
    background: rgba(231, 76, 60, 0.1);
}

/* ===============================
   النماذج
   =============================== */
.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--primary-color);
}

.required {
    color: var(--danger-color);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    font-size: 1rem;
    transition: var(--transition);
    font-family: inherit;
    background: var(--bg-color);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 4px rgba(14, 165, 233, 0.1);
    background: white;
    transform: translateY(-1px);
}

.form-group input:hover,
.form-group select:hover,
.form-group textarea:hover {
    border-color: var(--secondary-light);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
    flex-wrap: wrap;
}

/* ===============================
   أقسام النموذج
   =============================== */
.customer-form-section {
    margin: 2rem 0 1rem 0;
}

.customer-form-section h3 {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 12px 20px;
    border-radius: var(--border-radius);
    margin: 20px 0 15px 0;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
    box-shadow: var(--shadow);
}

.customer-form-section h3 i {
    font-size: 1.2em;
}

/* ===============================
   الإشعارات
   =============================== */
.toast-container {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 3000;
    display: flex;
    flex-direction: column;
    gap: 12px;
    pointer-events: none;
}

.toast {
    background: white;
    padding: 1.25rem 1.75rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-xl);
    border-right: 5px solid var(--success-color);
    display: flex;
    align-items: center;
    gap: 1rem;
    min-width: 320px;
    max-width: 450px;
    animation: toastSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    pointer-events: auto;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.toast::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 2px;
    background: var(--success-color);
    animation: toastProgress 3s linear;
}

.toast.error {
    border-right-color: var(--danger-color);
    background: linear-gradient(135deg, #ffffff 0%, #fef2f2 100%);
}

.toast.error::before {
    background: var(--danger-color);
}

.toast.warning {
    border-right-color: var(--warning-color);
    background: linear-gradient(135deg, #ffffff 0%, #fffbeb 100%);
}

.toast.warning::before {
    background: var(--warning-color);
}

.toast.info {
    border-right-color: var(--info-color);
    background: linear-gradient(135deg, #ffffff 0%, #f0f9ff 100%);
}

.toast.info::before {
    background: var(--info-color);
}

.toast.success {
    border-right-color: var(--success-color);
    background: linear-gradient(135deg, #ffffff 0%, #f0fdf4 100%);
}

.toast i {
    font-size: 1.25rem;
    opacity: 0.9;
}

.toast span {
    font-weight: 500;
    font-size: 0.95rem;
    line-height: 1.4;
}

@keyframes toastSlideIn {
    from {
        opacity: 0;
        transform: translateX(-100%) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

@keyframes toastProgress {
    from {
        width: 100%;
    }
    to {
        width: 0%;
    }
}

/* ===============================
   التقويم
   =============================== */
.calendar-container {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.calendar-nav-btn {
    background: var(--secondary-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.calendar-nav-btn:hover {
    background: #2980b9;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background: var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.calendar-day {
    background: white;
    padding: 1rem;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.calendar-day:hover {
    background: rgba(52, 152, 219, 0.1);
}

.calendar-day.today {
    background: var(--secondary-color);
    color: white;
}

.calendar-day.has-appointment {
    background: rgba(39, 174, 96, 0.1);
    border: 2px solid var(--success-color);
}

/* ===============================
   التبويبات
   =============================== */
.certificates-tabs {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.tab-buttons {
    display: flex;
    background: var(--light-color);
    border-bottom: 1px solid var(--border-color);
}

.tab-btn {
    flex: 1;
    padding: 1rem;
    border: none;
    background: transparent;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 500;
}

.tab-btn:hover {
    background: rgba(52, 152, 219, 0.1);
}

.tab-btn.active {
    background: var(--secondary-color);
    color: white;
}

.tab-content {
    display: none;
    padding: 2rem;
}

.tab-content.active {
    display: block;
}

/* ===============================
   الإعدادات
   =============================== */
.settings-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.settings-card {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.settings-card h3 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
    border-bottom: 2px solid var(--secondary-color);
    padding-bottom: 0.5rem;
}

.setting-item {
    margin-bottom: 1.5rem;
}

.setting-item label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--primary-color);
}

.setting-item input {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
}

.setting-item input:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.backup-controls {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    margin-bottom: 2rem;
}

.backup-list h4 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

/* ===============================
   تنسيق البحث في الزبائن
   =============================== */
.search-container {
    position: relative;
    margin-bottom: 20px;
}

.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
}

.search-results-list {
    padding: 0;
}

.search-result-item {
    padding: 12px 16px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    transition: background-color 0.2s;
}

.search-result-item:hover {
    background-color: #f8f9fa;
}

.search-result-item:last-child {
    border-bottom: none;
}

.customer-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.customer-details {
    font-size: 0.9em;
    color: #666;
}

.no-results {
    padding: 16px;
    text-align: center;
    color: #999;
    font-style: italic;
}

/* ===============================
   تنسيق أنواع الشهادات المتعددة
   =============================== */
.certificate-type-info {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    margin: 20px 0 15px 0;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.certificate-type-info i {
    font-size: 1.2em;
}

/* تنسيق زر طباعة الشهادة المحسن */
.print-certificate-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 8px;
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 20px auto;
}

.print-certificate-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.print-certificate-btn:active {
    transform: translateY(0);
}

.print-certificate-btn i {
    font-size: 1.2em;
}

/* ===============================
   الوضع المظلم
   =============================== */
.dark-mode {
    /* ألوان الخلفية المظلمة */
    --bg-color: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;

    /* ألوان النص المظلمة */
    --text-color: #f1f5f9;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;

    /* ألوان الحدود المظلمة */
    --border-color: #475569;
    --border-light: #334155;
    --border-dark: #64748b;

    /* ظلال محسنة للوضع المظلم */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.5), 0 10px 10px -5px rgba(0, 0, 0, 0.3);

    /* تدرجات محسنة للوضع المظلم */
    --gradient-primary: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    --gradient-secondary: linear-gradient(135deg, #0284c7 0%, #0ea5e9 100%);
    --gradient-success: linear-gradient(135deg, #059669 0%, #10b981 100%);
}

.dark-mode .card,
.dark-mode .table-container,
.dark-mode .modal-content,
.dark-mode .calendar-container,
.dark-mode .certificates-tabs,
.dark-mode .settings-card {
    background: #2d2d2d;
    color: #e0e0e0;
}

.dark-mode .search-results {
    background: #2d2d2d;
    border-color: #4a5568;
}

.dark-mode .search-result-item {
    border-bottom-color: #4a5568;
}

.dark-mode .search-result-item:hover {
    background-color: #4a5568;
}

.dark-mode .customer-name {
    color: #e2e8f0;
}

.dark-mode .customer-details {
    color: #a0aec0;
}

.dark-mode .no-results {
    color: #718096;
}

/* ===============================
   التصميم المتجاوب
   =============================== */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    nav {
        padding: 0 1rem;
    }
    
    nav ul {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    main {
        padding: 1rem;
    }
    
    .dashboard-cards {
        grid-template-columns: 1fr;
    }
    
    .action-bar {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-box {
        min-width: auto;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .modal-content {
        width: 95%;
        margin: 5% auto;
        padding: 1rem;
    }
    
    .form-buttons {
        flex-direction: column;
    }
    
    .backup-controls {
        flex-direction: column;
    }
    
    .settings-container {
        grid-template-columns: 1fr;
    }
}

/* تنسيقات جدول الإرسال */
.transmission-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.transmission-header {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    color: white;
    padding: 2rem;
    text-align: center;
}

.company-info h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.8rem;
    font-weight: bold;
}

.company-info h4 {
    margin: 0 0 1rem 0;
    font-size: 1.4rem;
    font-weight: 600;
    opacity: 0.9;
}

.company-info p {
    margin: 0.3rem 0;
    font-size: 1.1rem;
    opacity: 0.8;
}

.company-info .highlight {
    font-weight: bold;
    font-size: 1.2rem;
    margin: 1rem 0;
}

.transmission-alert {
    background: #fef3c7;
    border: 1px solid #f59e0b;
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
    display: flex;
    align-items: center;
    gap: 1rem;
    color: #92400e;
}

.transmission-alert i {
    color: #f59e0b;
    font-size: 1.2rem;
}

.transmission-controls {
    padding: 1.5rem;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.controls-left {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.controls-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.transmission-filters {
    padding: 1rem 1.5rem;
    background: white;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-group label {
    font-weight: 500;
    color: #374151;
}

.filter-group select {
    padding: 0.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    background: white;
    min-width: 120px;
}

.transmission-summary {
    padding: 1.5rem;
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.summary-card {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.summary-card h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
    opacity: 0.9;
}

.summary-card .count {
    font-size: 2rem;
    font-weight: bold;
    margin: 0;
}

/* تنسيقات أقسام نوع العملية */
.customer-form-section {
    margin: 1.5rem 0;
    padding: 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: #f8fafc;
}

.customer-form-section h3 {
    margin: 0 0 1rem 0;
    color: #1e3a8a;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.customer-form-section h3 i {
    color: #3b82f6;
}

.certificate-type-info,
.card-renewal-info {
    border-bottom: 1px solid #e2e8f0;
    padding-bottom: 0.5rem;
    margin-bottom: 1rem;
}

#card-renewal-section,
#certificate-type-section {
    transition: all 0.3s ease;
}

#card-renewal-section.show,
#certificate-type-section.show {
    display: block !important;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تحسين تنسيق النماذج */
.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #374151;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group .required {
    color: #ef4444;
}

/* تنسيقات الإعدادات */
.settings-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.settings-card {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.settings-card h3 {
    margin: 0 0 1.5rem 0;
    color: #1f2937;
    font-size: 1.25rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    border-bottom: 2px solid #e5e7eb;
    padding-bottom: 0.75rem;
}

.settings-card h3 i {
    color: #3b82f6;
}

.form-buttons {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e5e7eb;
}

.form-buttons .btn {
    padding: 0.75rem 1.5rem;
    font-weight: 500;
}

.form-buttons .btn.primary {
    background: #10b981;
    color: white;
    border: none;
}

.form-buttons .btn.primary:hover {
    background: #059669;
}

.form-buttons .btn.secondary {
    background: #6b7280;
    color: white;
    border: none;
}

.form-buttons .btn.secondary:hover {
    background: #4b5563;
}

/* تنسيق خاص للولايات */
#company-wilaya {
    max-height: 200px;
    overflow-y: auto;
}

/* تنسيقات حقل الدين */
.debt-input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.debt-input-container input {
    padding-right: 60px;
    flex: 1;
}

.debt-input-container .currency {
    position: absolute;
    right: 10px;
    color: #6b7280;
    font-weight: 500;
    pointer-events: none;
}

.debt-note {
    color: #6b7280;
    font-size: 0.8rem;
    margin-top: 0.25rem;
    font-style: italic;
}

/* تنسيق خاص للديون في الجداول */
.debt-amount {
    font-weight: bold;
    padding: 4px 8px;
    border-radius: 4px;
    text-align: center;
}

.debt-amount.no-debt {
    background: #d1fae5;
    color: #065f46;
}

.debt-amount.has-debt {
    background: #fee2e2;
    color: #991b1b;
}

.debt-amount.high-debt {
    background: #fecaca;
    color: #7f1d1d;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* ===============================
   نظام التقارير
   =============================== */
.reports-filters {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 2rem;
    display: flex;
    gap: 1.5rem;
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-group label {
    font-weight: 600;
    color: var(--text-color);
    font-size: 0.9rem;
}

.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.report-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 2rem;
    transition: var(--transition);
    border: 1px solid var(--border-light);
    display: flex;
    gap: 1.5rem;
    align-items: center;
}

.report-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: var(--secondary-light);
}

.report-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: var(--gradient-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    flex-shrink: 0;
}

.report-info {
    flex: 1;
}

.report-info h3 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    font-size: 1.25rem;
}

.report-info p {
    color: var(--text-secondary);
    margin-bottom: 1rem;
    line-height: 1.5;
}

.report-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    flex-wrap: wrap;
}

/* تصميم التقرير */
.report-container {
    max-width: 100%;
    margin: 0 auto;
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius);
}

.report-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--border-color);
}

.report-header h1 {
    color: var(--primary-color);
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.report-info {
    color: var(--text-secondary);
    font-size: 1rem;
}

.report-summary {
    margin-bottom: 3rem;
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.summary-card {
    background: var(--bg-secondary);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    text-align: center;
    border: 1px solid var(--border-light);
    transition: var(--transition);
}

.summary-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow);
}

.summary-card h3 {
    color: var(--text-color);
    font-size: 1rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.summary-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--primary-color);
    margin: 0;
}

.summary-card.revenue .summary-number {
    color: var(--success-color);
}

.summary-card.expenses .summary-number {
    color: var(--warning-color);
}

.summary-card.profit .summary-number {
    color: var(--info-color);
}

.report-details {
    margin-bottom: 2rem;
}

.report-footer {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* تحسين المظهر للشاشات الصغيرة */
@media (max-width: 768px) {
    .settings-card {
        padding: 1rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .form-buttons {
        flex-direction: column;
    }

    .debt-input-container .currency {
        right: 8px;
        font-size: 0.9rem;
    }

    .reports-filters {
        flex-direction: column;
        align-items: stretch;
    }

    .reports-grid {
        grid-template-columns: 1fr;
    }

    .report-card {
        flex-direction: column;
        text-align: center;
    }

    .report-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .summary-cards {
        grid-template-columns: 1fr;
    }

    .report-actions {
        flex-direction: column;
    }
}

/* ===============================
   نظام الإشعارات
   =============================== */
.notification-container {
    position: relative;
    display: inline-block;
}

.notification-btn {
    background: none;
    border: none;
    color: var(--text-color);
    font-size: 1.2rem;
    padding: 0.5rem;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
}

.notification-btn:hover {
    background: var(--bg-secondary);
    color: var(--primary-color);
}

.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: var(--danger-color);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    display: none;
}

.notification-panel {
    position: absolute;
    top: 100%;
    right: 0;
    width: 350px;
    max-height: 500px;
    background: white;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    display: none;
    overflow: hidden;
}

.notification-header {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--bg-secondary);
}

.notification-header h3 {
    margin: 0;
    font-size: 1.1rem;
    color: var(--text-color);
}

.notification-section {
    border-bottom: 1px solid var(--border-light);
}

.notification-section h4 {
    margin: 0;
    padding: 0.75rem 1rem 0.5rem;
    font-size: 0.9rem;
    color: var(--text-secondary);
    background: var(--bg-light);
}

.notification-item {
    display: flex;
    align-items: flex-start;
    padding: 1rem;
    border-bottom: 1px solid var(--border-light);
    cursor: pointer;
    transition: var(--transition);
    position: relative;
}

.notification-item:hover {
    background: var(--bg-light);
}

.notification-item.unread {
    background: rgba(59, 130, 246, 0.05);
    border-left: 3px solid var(--primary-color);
}

.notification-item.high-priority {
    border-left: 3px solid var(--danger-color);
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
}

.notification-icon.info {
    background: rgba(59, 130, 246, 0.1);
    color: var(--info-color);
}

.notification-icon.warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.notification-icon.error {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.notification-icon.success {
    background: rgba(34, 197, 94, 0.1);
    color: var(--success-color);
}

.notification-content {
    flex: 1;
    min-width: 0;
}

.notification-title {
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
}

.notification-message {
    color: var(--text-secondary);
    font-size: 0.85rem;
    line-height: 1.4;
    margin-bottom: 0.5rem;
}

.notification-time {
    color: var(--text-muted);
    font-size: 0.75rem;
}

.notification-dismiss {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
}

.notification-dismiss:hover {
    background: var(--bg-secondary);
    color: var(--text-color);
}

.notification-empty {
    padding: 2rem;
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
}

/* تحسين المظهر للشاشات الصغيرة */
@media (max-width: 768px) {
    .notification-panel {
        width: 300px;
        right: -50px;
    }

    .notification-item {
        padding: 0.75rem;
    }

    .notification-icon {
        width: 35px;
        height: 35px;
        margin-right: 0.75rem;
    }
}

/* تنسيقات إعدادات التذكيرات */
.reminder-input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.reminder-input-container input {
    padding-right: 80px;
    flex: 1;
}

.reminder-input-container .unit {
    position: absolute;
    right: 10px;
    color: #6b7280;
    font-weight: 500;
    pointer-events: none;
    font-size: 0.9rem;
}

.reminder-note {
    color: #6b7280;
    font-size: 0.8rem;
    margin-top: 0.25rem;
    font-style: italic;
}

/* معاينة التذكيرات */
.reminder-preview {
    margin-top: 2rem;
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.reminder-preview h4 {
    margin: 0 0 1rem 0;
    color: #1f2937;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.reminder-preview h4 i {
    color: #3b82f6;
}

.preview-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.preview-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: white;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.preview-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.preview-item i {
    font-size: 1.25rem;
    width: 24px;
    text-align: center;
}

.card-preview i {
    color: #10b981;
}

.debt-preview i {
    color: #f59e0b;
}

.monitoring-preview i {
    color: #3b82f6;
}

.preview-item span {
    font-weight: 500;
    color: #374151;
}

/* تنسيق خاص للـ checkboxes */
.form-group label input[type="checkbox"] {
    margin-left: 0.5rem;
    margin-right: 0;
    transform: scale(1.2);
}

/* أزرار إضافية */
.btn.info {
    background: #3b82f6;
    color: white;
    border: none;
}

.btn.info:hover {
    background: #2563eb;
}

/* تأثيرات التذكيرات */
.reminder-alert {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #fef3c7;
    border: 1px solid #f59e0b;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    max-width: 350px;
    animation: slideInRight 0.3s ease;
}

.reminder-alert.urgent {
    background: #fee2e2;
    border-color: #ef4444;
}

.reminder-alert .alert-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-weight: bold;
}

.reminder-alert .alert-header i {
    color: #f59e0b;
}

.reminder-alert.urgent .alert-header i {
    color: #ef4444;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* ===============================
   تنسيقات قسم الشهادات
   =============================== */

/* إحصائيات الشهادات */
.certificates-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.certificates-stats .stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.certificates-stats .stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.certificates-stats .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    font-size: 1.5rem;
    color: white;
}

.certificates-stats .stat-icon.installation {
    background: linear-gradient(135deg, #10b981, #059669);
}

.certificates-stats .stat-icon.monitoring {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.certificates-stats .stat-icon.warranty {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.certificates-stats .stat-icon.expiring {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.certificates-stats .stat-info h3 {
    font-size: 2rem;
    font-weight: bold;
    margin: 0 0 0.5rem 0;
    color: #1f2937;
}

.certificates-stats .stat-info p {
    margin: 0;
    color: #6b7280;
    font-weight: 500;
}

/* فلاتر الشهادات */
.certificates-filters {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    flex-wrap: wrap;
}

.certificates-filters select,
.certificates-filters input {
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.9rem;
    min-width: 150px;
}

.certificates-filters select:focus,
.certificates-filters input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* تبويبات التذكيرات */
.reminders-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.reminder-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-left: 4px solid;
    transition: transform 0.3s ease;
}

.reminder-card:hover {
    transform: translateY(-2px);
}

.reminder-card.urgent {
    border-left-color: #ef4444;
}

.reminder-card.warning {
    border-left-color: #f59e0b;
}

.reminder-card.info {
    border-left-color: #3b82f6;
}

.reminder-card .reminder-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    font-size: 1.2rem;
    color: white;
}

.reminder-card.urgent .reminder-icon {
    background: #ef4444;
}

.reminder-card.warning .reminder-icon {
    background: #f59e0b;
}

.reminder-card.info .reminder-icon {
    background: #3b82f6;
}

.reminder-card .reminder-info h4 {
    font-size: 1.8rem;
    font-weight: bold;
    margin: 0 0 0.25rem 0;
    color: #1f2937;
}

.reminder-card .reminder-info p {
    margin: 0 0 0.25rem 0;
    color: #374151;
    font-weight: 500;
}

.reminder-card .reminder-info small {
    color: #6b7280;
    font-size: 0.8rem;
}

/* تبويبات التذكيرات الفرعية */
.reminders-tabs {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.reminder-tab-buttons {
    display: flex;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
}

.reminder-tab-btn {
    flex: 1;
    padding: 1rem;
    background: none;
    border: none;
    cursor: pointer;
    font-weight: 500;
    color: #6b7280;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.reminder-tab-btn:hover {
    background: #e2e8f0;
    color: #374151;
}

.reminder-tab-btn.active {
    background: white;
    color: #3b82f6;
    border-bottom: 2px solid #3b82f6;
}

.reminder-tab-content {
    display: none;
    padding: 1.5rem;
}

.reminder-tab-content.active {
    display: block;
}

/* حالات الشهادات */
.certificate-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
}

.certificate-status.active {
    background: #d1fae5;
    color: #065f46;
}

.certificate-status.expired {
    background: #fee2e2;
    color: #991b1b;
}

.certificate-status.pending {
    background: #fef3c7;
    color: #92400e;
}

.certificate-status.passed {
    background: #d1fae5;
    color: #065f46;
}

.certificate-status.failed {
    background: #fee2e2;
    color: #991b1b;
}

.certificate-status.conditional {
    background: #fef3c7;
    color: #92400e;
}

/* أولوية التذكيرات */
.priority-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: bold;
    text-transform: uppercase;
}

.priority-badge.high {
    background: #fee2e2;
    color: #991b1b;
}

.priority-badge.medium {
    background: #fef3c7;
    color: #92400e;
}

.priority-badge.low {
    background: #e0f2fe;
    color: #0369a1;
}

/* ===============================
   تنسيقات قسم الموردين
   =============================== */

/* إحصائيات الموردين */
.suppliers-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.suppliers-stats .stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.suppliers-stats .stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.suppliers-stats .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    font-size: 1.5rem;
    color: white;
}

.suppliers-stats .stat-icon.active {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.suppliers-stats .stat-icon.products {
    background: linear-gradient(135deg, #10b981, #059669);
}

.suppliers-stats .stat-icon.orders {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.suppliers-stats .stat-icon.pending {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

/* فلاتر الموردين */
.suppliers-filters {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    flex-wrap: wrap;
}

.suppliers-filters select {
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.9rem;
    min-width: 150px;
}

.suppliers-filters select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* حالات الموردين */
.supplier-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
}

.supplier-status.active {
    background: #d1fae5;
    color: #065f46;
}

.supplier-status.inactive {
    background: #f3f4f6;
    color: #374151;
}

.supplier-status.blocked {
    background: #fee2e2;
    color: #991b1b;
}

/* فئات الموردين */
.supplier-category {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
}

.supplier-category.gas-equipment {
    background: #dbeafe;
    color: #1e40af;
}

.supplier-category.spare-parts {
    background: #d1fae5;
    color: #065f46;
}

.supplier-category.tools {
    background: #fef3c7;
    color: #92400e;
}

.supplier-category.services {
    background: #ede9fe;
    color: #6b21a8;
}

/* تقييم الموردين */
.supplier-rating {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.supplier-rating .stars {
    color: #fbbf24;
}

.supplier-rating .rating-text {
    font-size: 0.8rem;
    color: #6b7280;
}

/* نموذج المورد */
.form-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.form-section h4 {
    margin: 0 0 1rem 0;
    color: #1f2937;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.1rem;
    border-bottom: 1px solid #e2e8f0;
    padding-bottom: 0.5rem;
}

.form-section h4 i {
    color: #3b82f6;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .suppliers-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .suppliers-filters {
        flex-direction: column;
    }

    .suppliers-filters select {
        min-width: auto;
    }

    .form-section {
        padding: 1rem;
    }
}

/* تنسيقات الطباعة المحدثة */
@media print {
    /* إخفاء العناصر غير المرغوب فيها في الطباعة */
    .sidebar,
    .no-print,
    .transmission-controls,
    .transmission-filters,
    .transmission-summary,
    nav,
    .sidebar nav,
    .sidebar nav ul,
    .sidebar nav ul li {
        display: none !important;
        visibility: hidden !important;
    }

    /* تعديل التخطيط للطباعة */
    main {
        margin-right: 0 !important;
        margin-left: 0 !important;
        width: 100% !important;
    }

    /* تنسيق الحاويات */
    .certificate-container,
    .transmission-container {
        box-shadow: none;
        border-radius: 0;
        margin: 0 !important;
        padding: 10px !important;
    }

    /* تنسيق رأس الجدول */
    .transmission-header {
        background: white !important;
        color: black !important;
        border: 2px solid black;
    }

    /* تنسيق الجداول */
    th {
        background: #f0f0f0 !important;
        color: black !important;
    }

    td, th {
        border: 1px solid black !important;
        padding: 8px 4px !important;
        font-size: 12px !important;
    }

    table {
        page-break-inside: auto;
        width: 100% !important;
    }

    tr {
        page-break-inside: avoid;
        page-break-after: auto;
    }

    thead {
        display: table-header-group;
    }

    /* إخفاء أي عناصر تحكم إضافية */
    button,
    .btn,
    .action-bar,
    .search-box,
    .filters {
        display: none !important;
    }
}
