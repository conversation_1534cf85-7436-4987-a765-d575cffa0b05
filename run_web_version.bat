@echo off
title CFGPLProgram - Web Version Launcher
color 0A

echo.
echo ========================================
echo   CFGPLProgram - Web Version
echo   Future Fuel Corporation
echo ========================================
echo.
echo Node.js is not required for this version
echo Opening web version in your default browser...
echo.

REM Get the current directory
set "CURRENT_DIR=%~dp0"

REM Try to open the web version
start "" "%CURRENT_DIR%resources\app\index.html"

if %errorlevel% neq 0 (
    echo.
    echo ERROR: Could not open the web version!
    echo Please manually open: %CURRENT_DIR%resources\app\index.html
    echo.
    pause
    exit /b 1
)

echo.
echo Web version opened successfully!
echo.
echo Note: Some advanced features (like file operations)
echo may not work in the web version.
echo.
echo For full functionality, please install Node.js
echo and use the Electron version.
echo.
pause
