========================================
  CFGPLProgram - Quick Start Guide
  Future Fuel Corporation
========================================

🚀 QUICK START OPTIONS:

Option 1: Full Electron App (Recommended)
==========================================
Requirements: Node.js must be installed

1. Double-click: install.bat
2. Wait for installation to complete
3. Double-click: start.bat

Option 2: Web Version (No Node.js needed)
=========================================
Requirements: Any modern web browser

1. Double-click: run_web_version.bat
2. Application opens in browser

Option 3: Manual Installation
=============================
Requirements: Node.js + Command Prompt

1. Open Command Prompt in this folder
2. Run: npm install
3. Run: npm start

🔧 TROUBLESHOOTING:

Problem: "node is not recognized"
Solution: Install Node.js from https://nodejs.org

Problem: Installation fails
Solution: Run as Administrator

Problem: App won't start
Solution: Delete node_modules folder and run install.bat again

📞 SUPPORT:
- Email: <EMAIL>
- Phone: +213 XXX XXX XXX

========================================
Future Fuel Corporation
Gas and Licensing Management System
========================================
