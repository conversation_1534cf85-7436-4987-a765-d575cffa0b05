// ===============================
// بيانات التطبيق الأساسية
// ===============================
let appData = {
    gasCards: [],
    appointments: [],
    customers: [],
    vehicles: [],
    gasTanks: [],
    suppliers: [],
    inventory: [],
    sales: [],
    purchases: [],
    debts: [],
    certificates: [],
    settings: {
        companyName: 'Future Fuel Corporation',
        companyAddress: 'MEDEA, Algeria',
        expertName: '',
        issueLocation: 'MEDEA'
    }
};

// ===============================
// نظام اللغات المتعددة
// ===============================
let currentLanguage = 'ar';

const translations = {
    ar: {
        // القائمة الرئيسية
        dashboard: 'الرئيسية',
        gasCards: 'بطاقات الغاز',
        appointments: 'المواعيد',
        customers: 'الزبائن',
        suppliers: 'الموردين',
        inventory: 'المخزون',
        sales: 'المبيعات',
        purchases: 'المشتريات',
        debts: 'الديون',
        certificates: 'الشهادات',
        transmissionTable: 'جدول الإرسال',
        settings: 'الإعدادات',

        // الأزرار
        add: 'إضافة',
        edit: 'تعديل',
        delete: 'حذف',
        save: 'حفظ',
        cancel: 'إلغاء',
        print: 'طباعة',
        search: 'بحث',

        // نموذج الزبون
        addNewCustomer: 'إضافة زبون جديد',
        customerName: 'الاسم الكامل',
        customerPhone: 'رقم الهاتف',
        customerEmail: 'البريد الإلكتروني',
        customerAddress: 'العنوان',

        // أنواع العمليات
        operationType: 'نوع العملية',
        installation: 'تركيب',
        monitoring: 'مراقبة',
        cardRenewal: 'تجديد بطاقة'
    },
    fr: {
        // Menu principal
        dashboard: 'Accueil',
        gasCards: 'Cartes de Gaz',
        appointments: 'Rendez-vous',
        customers: 'Clients',
        suppliers: 'Fournisseurs',
        inventory: 'Inventaire',
        sales: 'Ventes',
        purchases: 'Achats',
        debts: 'Dettes',
        certificates: 'Certificats',
        transmissionTable: 'Table de Transmission',
        settings: 'Paramètres',

        // Boutons
        add: 'Ajouter',
        edit: 'Modifier',
        delete: 'Supprimer',
        save: 'Enregistrer',
        cancel: 'Annuler',
        print: 'Imprimer',
        search: 'Rechercher',

        // Formulaire client
        addNewCustomer: 'Ajouter Nouveau Client',
        customerName: 'Nom Complet',
        customerPhone: 'Téléphone',
        customerEmail: 'Email',
        customerAddress: 'Adresse',

        // Types d'opérations
        operationType: 'Type d\'Opération',
        installation: 'Installation',
        monitoring: 'Contrôle',
        cardRenewal: 'Renouvellement de Carte'
    }
};

// الأشهر بالفرنسية (تستخدم دائماً)
const frenchMonths = [
    'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
    'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
];

const frenchMonthsShort = [
    'Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun',
    'Jul', 'Aoû', 'Sep', 'Oct', 'Nov', 'Déc'
];

// ===============================
// وظائف اللغة
// ===============================

// الحصول على النص المترجم
function t(key) {
    return translations[currentLanguage][key] || key;
}

// تبديل اللغة
function switchLanguage(lang) {
    currentLanguage = lang;
    appData.settings.language = lang;
    saveData();
    updateLanguageUI();
}

// تحديث واجهة المستخدم حسب اللغة
function updateLanguageUI() {
    // تحديث اتجاه النص
    document.documentElement.dir = currentLanguage === 'ar' ? 'rtl' : 'ltr';
    document.documentElement.lang = currentLanguage;

    // تحديث نصوص القائمة
    updateNavigationTexts();

    // تحديث نصوص الأزرار
    updateButtonTexts();

    // تحديث نصوص النماذج
    updateFormTexts();
}

// تحديث نصوص القائمة
function updateNavigationTexts() {
    const navItems = {
        'dashboard': 'dashboard',
        'gas-cards': 'gasCards',
        'appointments': 'appointments',
        'customers': 'customers',
        'suppliers': 'suppliers',
        'inventory': 'inventory',
        'sales': 'sales',
        'purchases': 'purchases',
        'debts': 'debts',
        'certificates': 'certificates',
        'transmission-table': 'transmissionTable',
        'settings': 'settings'
    };

    Object.entries(navItems).forEach(([section, key]) => {
        const element = document.querySelector(`[data-section="${section}"]`);
        if (element) {
            const icon = element.querySelector('i');
            const iconClass = icon ? icon.className : '';
            element.innerHTML = `<i class="${iconClass}"></i> ${t(key)}`;
        }
    });
}

// تنسيق التاريخ بالأشهر الفرنسية
function formatDateWithFrenchMonth(dateString) {
    if (!dateString) return '';

    const date = new Date(dateString);
    const day = date.getDate();
    const month = frenchMonths[date.getMonth()];
    const year = date.getFullYear();

    return `${day} ${month} ${year}`;
}

// تنسيق التاريخ المختصر بالأشهر الفرنسية
function formatDateShortWithFrenchMonth(dateString) {
    if (!dateString) return '';

    const date = new Date(dateString);
    const day = date.getDate();
    const month = frenchMonthsShort[date.getMonth()];
    const year = date.getFullYear();

    return `${day} ${month} ${year}`;
}

// تحويل الأرقام إلى الفرنسية (الأرقام العربية تبقى كما هي)
function formatNumberFrench(number) {
    if (typeof number !== 'number') {
        number = parseFloat(number) || 0;
    }
    return number.toLocaleString('fr-FR');
}

// ===============================
// تحميل وحفظ البيانات (Electron)
// ===============================
async function loadData() {
    try {
        // محاولة تحميل البيانات من Electron أولاً
        if (window.electronAPI) {
            const result = await window.electronAPI.loadData();
            if (result.success) {
                appData = { ...appData, ...result.data };
                showToast('تم تحميل البيانات بنجاح');
                return;
            }
        }
        
        // العودة إلى localStorage كبديل
        const savedData = localStorage.getItem('cfgpl_data');
        if (savedData) {
            appData = { ...appData, ...JSON.parse(savedData) };
        }
    } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
        showToast('خطأ في تحميل البيانات', false);
    }
}

async function saveData() {
    console.log('💾 بدء حفظ البيانات...', {
        customersCount: appData.customers?.length || 0,
        gasCardsCount: appData.gasCards?.length || 0,
        appointmentsCount: appData.appointments?.length || 0
    });

    try {
        // حفظ في Electron أولاً
        if (window.electronAPI) {
            console.log('🔄 حفظ في Electron...');
            const result = await window.electronAPI.saveData(appData);
            if (result.success) {
                console.log('✅ تم حفظ البيانات في Electron بنجاح');
                showToast('تم حفظ البيانات بنجاح');
            } else {
                throw new Error(result.error);
            }
        } else {
            console.log('⚠️ Electron API غير متاح، الحفظ في localStorage فقط');
        }

        // حفظ في localStorage كنسخة احتياطية
        console.log('🔄 حفظ في localStorage...');
        localStorage.setItem('cfgpl_data', JSON.stringify(appData));
        console.log('✅ تم حفظ البيانات في localStorage بنجاح');

    } catch (error) {
        console.error('❌ خطأ في حفظ البيانات:', error);
        showToast('خطأ في حفظ البيانات', false);

        // محاولة الحفظ في localStorage على الأقل
        try {
            console.log('🔄 محاولة الحفظ في localStorage كبديل...');
            localStorage.setItem('cfgpl_data', JSON.stringify(appData));
            console.log('✅ تم الحفظ في localStorage كبديل');
        } catch (localError) {
            console.error('❌ خطأ في الحفظ المحلي:', localError);
        }
    }
}

// ===============================
// إدارة التاريخ والوقت
// ===============================
function updateDateTime() {
    const now = new Date();
    const dateElement = document.getElementById('current-date');
    const timeElement = document.getElementById('current-time');
    
    if (dateElement) {
        dateElement.textContent = now.toLocaleDateString('ar-SA', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }
    
    if (timeElement) {
        timeElement.textContent = now.toLocaleTimeString('ar-SA', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }
}

// ===============================
// إدارة الإشعارات
// ===============================
function showToast(message, isSuccess = true) {
    const container = document.getElementById('toast-container');
    if (!container) return;

    const toast = document.createElement('div');
    toast.className = `toast ${isSuccess ? 'success' : 'error'}`;
    toast.innerHTML = `
        <i class="fas ${isSuccess ? 'fa-check-circle' : 'fa-exclamation-circle'}"></i>
        <span>${message}</span>
    `;

    container.appendChild(toast);

    // إزالة الإشعار بعد 3 ثوان
    setTimeout(() => {
        toast.remove();
    }, 3000);
}

// ===============================
// إدارة التنقل
// ===============================
function setupNavigation() {
    const navLinks = document.querySelectorAll('nav a');
    
    if (navLinks.length === 0) {
        console.error('❌ لم يتم العثور على روابط التنقل');
        return;
    }
    
    navLinks.forEach((link) => {
        const sectionId = link.getAttribute('data-section');
        
        link.addEventListener('click', (e) => {
            e.preventDefault();

            // إزالة الفئة النشطة من جميع الروابط
            navLinks.forEach(l => l.classList.remove('active'));

            // إضافة الفئة النشطة إلى الرابط المنقر عليه
            link.classList.add('active');

            // إخفاء جميع الأقسام
            const allSections = document.querySelectorAll('main section');
            allSections.forEach(section => {
                section.classList.remove('active-section');
            });

            // إظهار القسم المطلوب
            const targetSection = document.getElementById(sectionId);
            if (targetSection) {
                targetSection.classList.add('active-section');
            } else {
                console.error(`❌ لم يتم العثور على القسم: ${sectionId}`);
            }

            // تحديث المحتوى حسب القسم
            updateSectionContent(sectionId);
        });
    });
}

function updateSectionContent(sectionId) {
    switch (sectionId) {
        case 'dashboard':
            updateDashboard();
            break;
        case 'gas-cards':
            updateGasCardsTable();
            break;
        case 'appointments':
            updateAppointmentsTable();
            updateCalendar();
            break;
        case 'customers':
            updateCustomersTable();
            break;
        case 'suppliers':
            updateSuppliersTable();
            break;
        case 'certificates':
            updateCertificatesTable();
            break;
        case 'transmission-table':
            updateTransmissionTable();
            updateTransmissionCompanyInfo();
            checkTransmissionAlert();
            break;
        case 'settings':
            updateBackupsList();
            break;
        default:
            break;
    }
}

// ===============================
// تحديث لوحة التحكم
// ===============================
function updateDashboard() {
    // تحديث الإحصائيات
    document.getElementById('total-cards').textContent = appData.gasCards.length;
    document.getElementById('total-appointments').textContent = appData.appointments.length;
    document.getElementById('total-customers').textContent = appData.customers.length;

    // حساب إجمالي الديون من الزبائن
    const totalDebts = appData.customers.reduce((sum, customer) => sum + (customer.debt || 0), 0);
    const totalDebtsEl = document.getElementById('total-debts');
    if (totalDebtsEl) {
        totalDebtsEl.textContent = `${formatNumberFrench(totalDebts)} دج`;
    }

    // تحديث التنبيهات
    updateAlerts();
}

function updateAlerts() {
    const settings = appData.settings.reminders || {};
    const cardReminderDays = settings.cardReminderDays || 30;

    // بطاقات تحتاج للتجديد
    const renewalCards = appData.gasCards.filter(card => {
        const expiryDate = new Date(card.expiryDate);
        const today = new Date();
        const diffTime = expiryDate - today;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return diffDays <= cardReminderDays && diffDays > 0;
    });

    // مواعيد اليوم
    const today = new Date().toISOString().split('T')[0];
    const todayAppointments = appData.appointments.filter(apt => apt.date === today);

    // زبائن لديهم ديون
    const highDebtThreshold = settings.highDebtThreshold || 5000;
    const customersWithDebts = appData.customers.filter(customer => (customer.debt || 0) > 0);
    const highDebtCustomers = customersWithDebts.filter(customer => customer.debt >= highDebtThreshold);

    document.getElementById('cards-renewal-count').textContent = `${renewalCards.length} بطاقة`;
    document.getElementById('today-appointments-count').textContent = `${todayAppointments.length} موعد`;

    const overdueDebtsEl = document.getElementById('overdue-debts-count');
    if (overdueDebtsEl) {
        overdueDebtsEl.textContent = `${customersWithDebts.length} زبون`;
        if (highDebtCustomers.length > 0) {
            overdueDebtsEl.style.color = '#dc2626';
            overdueDebtsEl.title = `${highDebtCustomers.length} زبون لديهم ديون عالية (أكثر من 5000 دج)`;
        }
    }
}

// ===============================
// إدارة الجداول
// ===============================
function updateGasCardsTable() {
    const tableBody = document.querySelector('#cards-table tbody');
    if (!tableBody) return;

    tableBody.innerHTML = '';

    appData.gasCards.forEach(card => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${card.cardNumber}</td>
            <td>${card.customerName}</td>
            <td>${card.plateNumber}</td>
            <td>${formatDate(card.issueDate)}</td>
            <td>${formatDate(card.expiryDate)}</td>
            <td><span class="status ${card.status}">${getStatusText(card.status)}</span></td>
            <td>
                <button class="btn btn-sm" onclick="editGasCard('${card.id}')">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-sm danger" onclick="deleteGasCard('${card.id}')">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tableBody.appendChild(row);
    });
}

function updateAppointmentsTable() {
    const tableBody = document.querySelector('#appointments-table tbody');
    if (!tableBody) return;

    tableBody.innerHTML = '';

    appData.appointments.forEach(appointment => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${formatDate(appointment.date)}</td>
            <td>${appointment.time}</td>
            <td>${appointment.customerName}</td>
            <td>${appointment.serviceType}</td>
            <td><span class="status ${appointment.status}">${getStatusText(appointment.status)}</span></td>
            <td>${appointment.notes || '-'}</td>
            <td>
                <button class="btn btn-sm" onclick="editAppointment('${appointment.id}')">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-sm danger" onclick="deleteAppointment('${appointment.id}')">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tableBody.appendChild(row);
    });
}

function updateCustomersTable() {
    const tableBody = document.querySelector('#customers-table tbody');
    if (!tableBody) return;

    tableBody.innerHTML = '';

    appData.customers.forEach(customer => {
        const row = document.createElement('tr');
        const debtAmount = customer.debt || 0;
        const debtDisplay = formatDebtAmount(debtAmount);

        row.innerHTML = `
            <td>${customer.name}</td>
            <td>${customer.phone}</td>
            <td>${debtDisplay}</td>
            <td>${customer.address}</td>
            <td>${formatDate(customer.registrationDate)}</td>
            <td>
                <button class="btn btn-sm" onclick="editCustomer('${customer.id}')">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-sm info" onclick="manageCustomerDebt('${customer.id}')" title="إدارة الدين">
                    <i class="fas fa-money-bill-wave"></i>
                </button>
                <button class="btn btn-sm success" onclick="generateCustomerCertificate('${customer.id}')">
                    <i class="fas fa-print"></i>
                </button>
                <button class="btn btn-sm danger" onclick="deleteCustomer('${customer.id}')">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tableBody.appendChild(row);
    });
}

function updateCertificatesTable() {
    // تحديث جداول الشهادات المختلفة
    updateInstallationCertificatesTable();
    updateMonitoringCertificatesTable();
    updateCertificateReminders();
}

function updateInstallationCertificatesTable() {
    const tableBody = document.querySelector('#installation-certificates-table tbody');
    if (!tableBody) return;

    tableBody.innerHTML = '';

    // التأكد من وجود بيانات الشهادات
    if (!appData.certificates || !Array.isArray(appData.certificates)) {
        appData.certificates = [];
    }

    const installationCerts = appData.certificates.filter(cert => cert && cert.type === 'installation');

    if (installationCerts.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td colspan="5" style="text-align: center; color: #6b7280; padding: 2rem;">
                <i class="fas fa-certificate" style="font-size: 2rem; margin-bottom: 1rem; display: block;"></i>
                لا توجد شهادات تركيب
            </td>
        `;
        tableBody.appendChild(row);
        return;
    }

    installationCerts.forEach(cert => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${cert.certificateNumber || 'غير محدد'}</td>
            <td>${cert.customerName || 'غير محدد'}</td>
            <td>${cert.plateNumber || 'غير محدد'}</td>
            <td>${formatDate(cert.issueDate)}</td>
            <td>
                <button class="btn btn-sm" onclick="printCertificateOld('${cert.id}')" title="طباعة">
                    <i class="fas fa-print"></i>
                </button>
                <button class="btn btn-sm danger" onclick="deleteCertificateOld('${cert.id}')" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tableBody.appendChild(row);
    });
}

function updateMonitoringCertificatesTable() {
    const tableBody = document.querySelector('#monitoring-certificates-table tbody');
    if (!tableBody) return;

    tableBody.innerHTML = '';

    // التأكد من وجود بيانات الشهادات
    if (!appData.certificates || !Array.isArray(appData.certificates)) {
        appData.certificates = [];
    }

    const monitoringCerts = appData.certificates.filter(cert => cert && cert.type === 'monitoring');

    if (monitoringCerts.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td colspan="6" style="text-align: center; color: #6b7280; padding: 2rem;">
                <i class="fas fa-search-plus" style="font-size: 2rem; margin-bottom: 1rem; display: block;"></i>
                لا توجد شهادات مراقبة
            </td>
        `;
        tableBody.appendChild(row);
        return;
    }

    monitoringCerts.forEach(cert => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${cert.certificateNumber || 'غير محدد'}</td>
            <td>${cert.customerName || 'غير محدد'}</td>
            <td>${cert.plateNumber || 'غير محدد'}</td>
            <td>${formatDate(cert.issueDate)}</td>
            <td>${formatDate(cert.nextMonitoring)}</td>
            <td>
                <button class="btn btn-sm" onclick="printCertificateOld('${cert.id}')" title="طباعة">
                    <i class="fas fa-print"></i>
                </button>
                <button class="btn btn-sm danger" onclick="deleteCertificateOld('${cert.id}')" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tableBody.appendChild(row);
    });
}

function updateCertificateReminders() {
    // تحديث تذكيرات الشهادات
    const urgentTableBody = document.querySelector('#urgent-reminders-table tbody');
    const upcomingTableBody = document.querySelector('#upcoming-reminders-table tbody');

    if (urgentTableBody) {
        urgentTableBody.innerHTML = `
            <tr>
                <td colspan="7" style="text-align: center; color: #6b7280; padding: 2rem;">
                    <i class="fas fa-bell" style="font-size: 2rem; margin-bottom: 1rem; display: block;"></i>
                    لا توجد تذكيرات عاجلة
                </td>
            </tr>
        `;
    }

    if (upcomingTableBody) {
        upcomingTableBody.innerHTML = `
            <tr>
                <td colspan="6" style="text-align: center; color: #6b7280; padding: 2rem;">
                    <i class="fas fa-clock" style="font-size: 2rem; margin-bottom: 1rem; display: block;"></i>
                    لا توجد تذكيرات قريبة
                </td>
            </tr>
        `;
    }

    // منطق التذكيرات سيتم إضافته لاحقاً
}

// دوال مساعدة للشهادات القديمة
function printCertificateOld(certificateId) {
    showToast('ميزة الطباعة قيد التطوير');
}

function deleteCertificateOld(certificateId) {
    if (confirm('هل أنت متأكد من حذف هذه الشهادة؟')) {
        // البحث في جميع أنواع الشهادات
        if (appData.certificates && Array.isArray(appData.certificates)) {
            appData.certificates = appData.certificates.filter(cert => cert.id !== certificateId);
            saveData();
            updateCertificatesTable();
            showToast('تم حذف الشهادة بنجاح');
        }
    }
}

// ===============================
// إدارة النسخ الاحتياطية (Electron)
// ===============================
async function updateBackupsList() {
    if (!window.electronAPI) return;

    try {
        const result = await window.electronAPI.getBackupsList();
        if (result.success) {
            const backupSelect = document.getElementById('backup-select');
            const restoreBtn = document.getElementById('restore-backup-btn');

            if (backupSelect) {
                backupSelect.innerHTML = '';

                if (result.backups.length === 0) {
                    backupSelect.innerHTML = '<option value="">لا توجد نسخ احتياطية</option>';
                    restoreBtn.disabled = true;
                } else {
                    backupSelect.innerHTML = '<option value="">اختر نسخة احتياطية</option>';
                    result.backups.forEach(backup => {
                        const option = document.createElement('option');
                        option.value = backup.path;
                        option.textContent = `${backup.name} - ${formatDate(backup.date)} (${formatFileSize(backup.size)})`;
                        backupSelect.appendChild(option);
                    });

                    backupSelect.addEventListener('change', () => {
                        restoreBtn.disabled = !backupSelect.value;
                    });
                }
            }
        }
    } catch (error) {
        console.error('خطأ في تحديث قائمة النسخ الاحتياطية:', error);
    }
}

async function createBackup() {
    if (!window.electronAPI) {
        showToast('النسخ الاحتياطية متاحة فقط في تطبيق Electron', false);
        return;
    }

    try {
        const result = await window.electronAPI.createBackup(appData);
        if (result.success) {
            showToast('تم إنشاء النسخة الاحتياطية بنجاح');
            updateBackupsList();
        } else {
            throw new Error(result.error);
        }
    } catch (error) {
        console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
        showToast('خطأ في إنشاء النسخة الاحتياطية', false);
    }
}

async function restoreBackup() {
    const backupSelect = document.getElementById('backup-select');
    if (!backupSelect || !backupSelect.value) {
        showToast('يرجى اختيار نسخة احتياطية أولاً', false);
        return;
    }

    if (!confirm('هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟ سيتم استبدال جميع البيانات الحالية.')) {
        return;
    }

    try {
        const result = await window.electronAPI.restoreBackup(backupSelect.value);
        if (result.success) {
            appData = { ...appData, ...result.data };
            showToast('تم استعادة النسخة الاحتياطية بنجاح');
            updateDashboard();
            updateCustomersTable();
            updateCertificatesTable();
        } else {
            throw new Error(result.error);
        }
    } catch (error) {
        console.error('خطأ في استعادة النسخة الاحتياطية:', error);
        showToast('خطأ في استعادة النسخة الاحتياطية', false);
    }
}

async function exportData() {
    if (!window.electronAPI) {
        // تصدير عادي للمتصفح
        const dataStr = JSON.stringify(appData, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `cfgpl-data-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        URL.revokeObjectURL(url);
        showToast('تم تصدير البيانات بنجاح');
        return;
    }

    // تصدير Electron سيتم التعامل معه من القائمة
    showToast('استخدم قائمة ملف > تصدير لحفظ البيانات');
}

async function importData() {
    if (!window.electronAPI) {
        // استيراد عادي للمتصفح
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        input.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const importedData = JSON.parse(e.target.result);
                        appData = { ...appData, ...importedData };
                        saveData();
                        updateDashboard();
                        updateCustomersTable();
                        updateCertificatesTable();
                        showToast('تم استيراد البيانات بنجاح');
                    } catch (error) {
                        showToast('خطأ في قراءة الملف', false);
                    }
                };
                reader.readAsText(file);
            }
        };
        input.click();
        return;
    }

    // استيراد Electron سيتم التعامل معه من القائمة
    showToast('استخدم قائمة ملف > فتح لاستيراد البيانات');
}

// ===============================
// إدارة الزبائن
// ===============================
function setupCustomerForm() {
    console.log('🔧 إعداد نموذج الزبائن...');

    const customerForm = document.getElementById('customer-form');
    console.log('🔍 البحث عن نموذج الزبون...', customerForm);

    if (customerForm) {
        console.log('✅ تم العثور على نموذج الزبائن');
        customerForm.addEventListener('submit', (e) => {
            console.log('📝 تم إرسال النموذج');
            saveCustomer(e);
        });
        console.log('✅ تم ربط حدث الإرسال');

        // إضافة مستمع للزر مباشرة أيضاً
        const submitBtn = customerForm.querySelector('button[type="submit"]');
        console.log('🔍 البحث عن زر الإرسال...', submitBtn);

        if (submitBtn) {
            console.log('✅ تم العثور على زر الإرسال');
            submitBtn.addEventListener('click', (e) => {
                console.log('🖱️ تم النقر على زر الحفظ مباشرة');
                e.preventDefault();
                saveCustomer(e);
            });
        }
    } else {
        console.error('❌ لم يتم العثور على نموذج الزبائن');
        // محاولة البحث مرة أخرى بعد تأخير
        setTimeout(() => {
            const retryForm = document.getElementById('customer-form');
            console.log('🔄 محاولة ثانية للعثور على النموذج:', retryForm);
            if (retryForm) {
                retryForm.addEventListener('submit', (e) => {
                    console.log('📝 تم إرسال النموذج (محاولة ثانية)');
                    saveCustomer(e);
                });
            }
        }, 1000);
    }

    // إعداد البحث في الزبائن
    const customerSearch = document.getElementById('customer-search');
    if (customerSearch) {
        customerSearch.addEventListener('input', handleCustomerSearch);
    }

    // إعداد أزرار النموذج
    const addCustomerBtn = document.getElementById('add-customer-btn');
    console.log('🔍 البحث عن زر إضافة زبون...', addCustomerBtn);

    if (addCustomerBtn) {
        console.log('✅ تم العثور على زر إضافة زبون');
        addCustomerBtn.addEventListener('click', (e) => {
            e.preventDefault();
            console.log('🖱️ تم النقر على زر إضافة زبون');
            openCustomerModal();
        });
    } else {
        console.error('❌ لم يتم العثور على زر إضافة زبون');
        // محاولة البحث مرة أخرى بعد تأخير
        setTimeout(() => {
            const retryBtn = document.getElementById('add-customer-btn');
            console.log('🔄 محاولة ثانية للعثور على الزر:', retryBtn);
            if (retryBtn) {
                retryBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log('🖱️ تم النقر على زر إضافة زبون (محاولة ثانية)');
                    openCustomerModal();
                });
            }
        }, 1000);
    }

    const cancelCustomerBtn = document.getElementById('cancel-customer');
    if (cancelCustomerBtn) {
        cancelCustomerBtn.addEventListener('click', () => closeCustomerModal());
    }
}

function openCustomerModal(customerId = null) {
    console.log('🔓 فتح نافذة الزبون...', customerId);

    const modal = document.getElementById('customer-modal');
    const form = document.getElementById('customer-form');
    const title = document.getElementById('customer-modal-title');

    console.log('🔍 العناصر المطلوبة:', {
        modal: !!modal,
        form: !!form,
        title: !!title
    });

    if (!modal) {
        console.error('❌ لم يتم العثور على نافذة الزبون');
        showToast('خطأ: لم يتم العثور على نافذة الزبون', false);
        return;
    }

    if (!form) {
        console.error('❌ لم يتم العثور على نموذج الزبون');
        showToast('خطأ: لم يتم العثور على نموذج الزبون', false);
        return;
    }

    console.log('✅ تم العثور على جميع العناصر المطلوبة');

    if (customerId) {
        if (title) title.textContent = 'تعديل بيانات الزبون';
        loadCustomerData(customerId);
    } else {
        if (title) title.textContent = '🧾 نموذج إضافة زبون جديد';
        form.reset();
        const customerIdField = document.getElementById('customer-id');
        if (customerIdField) customerIdField.value = '';

        // تعيين تاريخ اليوم كافتراضي
        const operationDateField = document.getElementById('operation-date');
        if (operationDateField) {
            operationDateField.value = new Date().toISOString().split('T')[0];
        }
    }

    modal.style.display = 'block';
    console.log('✅ تم فتح النافذة بنجاح');
}

function closeCustomerModal() {
    const modal = document.getElementById('customer-modal');
    modal.style.display = 'none';
}

function saveCustomer(e) {
    console.log('💾 دالة حفظ الزبون تم استدعاؤها', e);

    if (e) {
        e.preventDefault();
        console.log('✅ تم منع السلوك الافتراضي');
    }

    console.log('🔄 بدء عملية حفظ الزبون...');

    const customerId = document.getElementById('customer-id').value;
    console.log('🆔 معرف الزبون:', customerId);

    // التحقق من وجود العناصر المطلوبة
    const nameField = document.getElementById('customer-name');
    const phoneField = document.getElementById('customer-phone');
    const debtField = document.getElementById('customer-debt');
    const addressField = document.getElementById('customer-address');

    console.log('🔍 فحص العناصر:', {
        nameField: !!nameField,
        phoneField: !!phoneField,
        debtField: !!debtField,
        addressField: !!addressField
    });

    if (!nameField || !phoneField || !addressField) {
        console.error('❌ بعض العناصر المطلوبة مفقودة');
        showToast('خطأ: لم يتم العثور على جميع حقول النموذج', false);
        return;
    }

    const customerData = {
        id: customerId || generateId(),
        name: nameField.value,
        phone: phoneField.value,
        debt: parseFloat(debtField?.value) || 0,
        address: addressField.value,
        registrationDate: customerId ?
            appData.customers.find(c => c.id === customerId)?.registrationDate :
            new Date().toISOString().split('T')[0]
    };

    console.log('👤 بيانات الزبون:', customerData);

    // التحقق من صحة البيانات الأساسية
    if (!customerData.name || !customerData.phone || !customerData.address) {
        console.error('❌ بيانات مفقودة:', {
            name: !customerData.name,
            phone: !customerData.phone,
            address: !customerData.address
        });
        showToast('يرجى ملء جميع الحقول المطلوبة', false);
        return;
    }

    console.log('✅ البيانات صحيحة، المتابعة للحفظ...');

    // التأكد من تهيئة مصفوفة الزبائن
    if (!appData.customers || !Array.isArray(appData.customers)) {
        console.log('⚠️ تهيئة مصفوفة الزبائن...');
        appData.customers = [];
    }

    console.log('📊 عدد الزبائن الحالي:', appData.customers.length);

    if (customerId) {
        // تحديث زبون موجود
        console.log('🔄 تحديث زبون موجود...');
        const index = appData.customers.findIndex(c => c.id === customerId);
        if (index !== -1) {
            appData.customers[index] = customerData;
            console.log('✅ تم تحديث الزبون في الفهرس:', index);
        } else {
            console.error('❌ لم يتم العثور على الزبون للتحديث');
        }
    } else {
        // إضافة زبون جديد
        console.log('➕ إضافة زبون جديد...');
        appData.customers.push(customerData);
        console.log('✅ تم إضافة الزبون، العدد الجديد:', appData.customers.length);
    }

    // التعامل مع نوع العملية المختار
    const operationType = document.getElementById('operation-type').value;
    console.log('📋 نوع العملية المختار:', operationType);

    if (operationType === 'card-renewal') {
        // تجديد بطاقة - حفظ في قسم البطاقات
        console.log('🆔 معالجة تجديد البطاقة...');

        const cardData = {
            oldCardNumber: document.getElementById('old-card-number').value,
            cardExpiryDate: document.getElementById('card-expiry-date').value,
            renewalReason: document.getElementById('renewal-reason').value,
            notes: document.getElementById('renewal-notes').value
        };

        console.log('🆔 بيانات البطاقة:', cardData);

        addGasCard(customerData, cardData);
        showToast('تم إضافة الزبون وتجديد البطاقة بنجاح');

    } else if (operationType === 'installation' || operationType === 'monitoring') {
        // تركيب أو مراقبة - حفظ بيانات السيارة والخزان وإضافة لجدول الإرسال
        console.log('🔧 معالجة تركيب/مراقبة...');

        // التحقق من وجود بيانات السيارة والخزان
        const plateNumber = document.getElementById('vehicle-plate-number').value;
        const vehicleBrand = document.getElementById('vehicle-brand').value;
        const tankSerial = document.getElementById('tank-serial-number').value;

        if (!plateNumber || !vehicleBrand || !tankSerial) {
            showToast('يرجى ملء بيانات السيارة والخزان المطلوبة', false);
            return;
        }

        const vehicleData = saveVehicleAndTankData(customerData.id);
        console.log('🚗 بيانات السيارة والخزان:', vehicleData);

        if (vehicleData && vehicleData.vehicle && vehicleData.tank) {
            // إضافة إلى جدول الإرسال
            const operationTypeArabic = operationType === 'installation' ? 'تركيب' : 'مراقبة';
            addToTransmissionTable(operationTypeArabic, customerData, vehicleData.vehicle, vehicleData.tank);

            // إنشاء شهادة إذا تم اختيار نوع شهادة
            const certificateType = document.getElementById('certificate-type').value;
            if (certificateType) {
                createCertificateRecord(customerData, vehicleData.vehicle, vehicleData.tank, certificateType);
            }
        }

        showToast(`تم إضافة الزبون وعملية ${operationTypeArabic} بنجاح`);
    } else {
        // حفظ عادي بدون عملية محددة
        saveVehicleAndTankData(customerData.id);
        showToast(customerId ? 'تم تحديث بيانات الزبون بنجاح' : 'تم إضافة الزبون بنجاح');
    }

    saveData();
    updateCustomersTable();
    updateDashboard();
    closeCustomerModal();
}

function saveVehicleAndTankData(customerId) {
    // حفظ بيانات السيارة
    const vehicleData = {
        id: generateId(),
        customerId: customerId,
        plateNumber: document.getElementById('vehicle-plate-number').value,
        brand: document.getElementById('vehicle-brand').value,
        model: document.getElementById('vehicle-model').value,
        chassisNumber: document.getElementById('vehicle-chassis-number').value,
        orderNumber: document.getElementById('vehicle-order-number').value,
        year: document.getElementById('vehicle-year').value
    };

    // حفظ بيانات الخزان
    const tankData = {
        id: generateId(),
        vehicleId: vehicleData.id,
        type: document.getElementById('tank-type').value,
        brand: document.getElementById('tank-brand').value,
        serialNumber: document.getElementById('tank-serial-number').value,
        capacity: document.getElementById('tank-capacity').value,
        manufactureYear: document.getElementById('tank-manufacture-year').value,
        manufactureDate: document.getElementById('tank-manufacture-date').value
    };

    // إزالة البيانات القديمة إذا كانت موجودة
    appData.vehicles = appData.vehicles.filter(v => v.customerId !== customerId);
    appData.gasTanks = appData.gasTanks.filter(t => {
        const vehicle = appData.vehicles.find(v => v.id === t.vehicleId);
        return !vehicle || vehicle.customerId !== customerId;
    });

    // إضافة البيانات الجديدة
    appData.vehicles.push(vehicleData);
    appData.gasTanks.push(tankData);

    // إرجاع البيانات للاستخدام
    return {
        vehicle: vehicleData,
        tank: tankData
    };
}

// ===============================
// البحث في الزبائن
// ===============================
function handleCustomerSearch(e) {
    const searchTerm = e.target.value.toLowerCase().trim();
    const searchResults = document.getElementById('search-results');

    if (searchTerm.length < 2) {
        if (searchResults) {
            searchResults.style.display = 'none';
        }
        return;
    }

    // البحث في الزبائن الموجودين
    const filteredCustomers = appData.customers.filter(customer => {
        const name = customer.name?.toLowerCase() || '';
        const phone = customer.phone?.toLowerCase() || '';

        // البحث في بيانات السيارة المرتبطة
        const vehicle = appData.vehicles.find(v => v.customerId === customer.id);
        const plateNumber = vehicle?.plateNumber?.toLowerCase() || '';

        return name.includes(searchTerm) ||
               phone.includes(searchTerm) ||
               plateNumber.includes(searchTerm);
    });

    // عرض نتائج البحث
    if (searchResults && filteredCustomers.length > 0) {
        let resultsHTML = '<div class="search-results-list">';
        filteredCustomers.slice(0, 5).forEach(customer => {
            const vehicle = appData.vehicles.find(v => v.customerId === customer.id);
            resultsHTML += `
                <div class="search-result-item" onclick="selectCustomerFromSearch('${customer.id}')">
                    <div class="customer-name">${customer.name}</div>
                    <div class="customer-details">${customer.phone} ${vehicle ? '- ' + vehicle.plateNumber : ''}</div>
                </div>
            `;
        });
        resultsHTML += '</div>';
        searchResults.innerHTML = resultsHTML;
        searchResults.style.display = 'block';
    } else if (searchResults) {
        searchResults.innerHTML = '<div class="no-results">لا توجد نتائج</div>';
        searchResults.style.display = 'block';
    }
}

function selectCustomerFromSearch(customerId) {
    const customer = appData.customers.find(c => c.id === customerId);
    if (customer) {
        // ملء النموذج ببيانات الزبون المختار
        loadCustomerData(customerId);

        // إخفاء نتائج البحث
        const searchResults = document.getElementById('search-results');
        if (searchResults) {
            searchResults.style.display = 'none';
        }
        document.getElementById('customer-search').value = customer.name;
    }
}

function loadCustomerData(customerId) {
    const customer = appData.customers.find(c => c.id === customerId);
    if (!customer) return;

    // ملء بيانات الزبون
    document.getElementById('customer-id').value = customer.id;
    document.getElementById('customer-name').value = customer.name;
    document.getElementById('customer-phone').value = customer.phone;
    document.getElementById('customer-debt').value = customer.debt || 0;
    document.getElementById('customer-address').value = customer.address;

    // ملء بيانات السيارة
    const vehicle = appData.vehicles.find(v => v.customerId === customerId);
    if (vehicle) {
        document.getElementById('vehicle-plate-number').value = vehicle.plateNumber || '';
        document.getElementById('vehicle-brand').value = vehicle.brand || '';
        document.getElementById('vehicle-model').value = vehicle.model || '';
        document.getElementById('vehicle-chassis-number').value = vehicle.chassisNumber || '';
        document.getElementById('vehicle-order-number').value = vehicle.orderNumber || '';
        document.getElementById('vehicle-year').value = vehicle.year || '';

        // ملء بيانات الخزان
        const tank = appData.gasTanks.find(t => t.vehicleId === vehicle.id);
        if (tank) {
            document.getElementById('tank-type').value = tank.type || '';
            document.getElementById('tank-brand').value = tank.brand || '';
            document.getElementById('tank-serial-number').value = tank.serialNumber || '';
            document.getElementById('tank-capacity').value = tank.capacity || '';
            document.getElementById('tank-manufacture-year').value = tank.manufactureYear || '';
            document.getElementById('tank-manufacture-date').value = tank.manufactureDate || '';
        }
    }
}

// ===============================
// إدارة الشهادات
// ===============================
function generateCustomerCertificate(customerId, certificateType = 'etancheite') {
    const customer = appData.customers.find(c => c.id === customerId);
    if (!customer) {
        showToast('لم يتم العثور على بيانات الزبون', false);
        return;
    }

    const vehicle = appData.vehicles.find(v => v.customerId === customerId);
    const gasTank = appData.gasTanks.find(t => t.vehicleId === vehicle?.id);

    if (!vehicle || !gasTank) {
        showToast('لم يتم العثور على بيانات السيارة أو الخزان', false);
        return;
    }

    // تحديد نوع الشهادة من النموذج إذا كان متاحاً
    const operationType = document.getElementById('operation-type')?.value || certificateType;

    // إنشاء رقم الشهادة حسب النوع
    const certificateNumber = generateCertificateNumberByType(operationType);
    const currentDate = new Date().toISOString().split('T')[0];

    // إنشاء HTML للشهادة حسب النوع
    const certificateHTML = createCertificateHTMLByType(operationType, customer, vehicle, gasTank, certificateNumber, currentDate);

    // فتح نافذة طباعة جديدة
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>شهادة ${getCertificateTypeName(operationType)} - ${customer.name}</title>
            <meta charset="UTF-8">
            <style>
                @media print {
                    body { margin: 0; }
                    .certificate-container { box-shadow: none !important; }
                }
                body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
            </style>
        </head>
        <body>
            ${certificateHTML}
        </body>
        </html>
    `);
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();

    // حفظ الشهادة في البيانات
    const certificateData = {
        id: generateId(),
        customerId: customerId,
        customerName: customer.name,
        plateNumber: vehicle.plateNumber,
        type: operationType,
        certificateNumber: certificateNumber,
        issueDate: currentDate,
        nextMonitoring: operationType === 'monitoring' ?
            new Date(Date.now() + 6 * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] : null
    };

    appData.certificates.push(certificateData);

    // إضافة إلى جدول الإرسال إذا كان النوع تركيب أو مراقبة
    const operationTypeArabic = getOperationTypeArabic(operationType);
    if (operationTypeArabic === 'تركيب' || operationTypeArabic === 'مراقبة') {
        addToTransmissionTable(operationTypeArabic, customer, vehicle, gasTank);
    }

    saveData();
    updateCertificatesTable();
}

function printSelectedCertificate() {
    const certificateType = document.getElementById('operation-type')?.value;
    const customerId = document.getElementById('customer-id')?.value;

    if (!certificateType) {
        showToast('يرجى اختيار نوع الشهادة أولاً', false);
        return;
    }

    if (!customerId) {
        showToast('يرجى حفظ بيانات الزبون أولاً قبل طباعة الشهادة', false);
        return;
    }

    // التحقق من وجود البيانات المطلوبة
    const customerName = document.getElementById('customer-name')?.value;
    const plateNumber = document.getElementById('vehicle-plate-number')?.value;
    const tankBrand = document.getElementById('tank-brand')?.value;

    if (!customerName || !plateNumber || !tankBrand) {
        showToast('يرجى ملء جميع البيانات المطلوبة قبل طباعة الشهادة', false);
        return;
    }

    // طباعة الشهادة
    generateCustomerCertificate(customerId, certificateType);
    showToast(`تم إرسال شهادة ${getCertificateTypeName(certificateType)} للطباعة`, true);
}

// ===============================
// دوال مساعدة للشهادات
// ===============================
function generateCertificateNumberByType(type) {
    const year = new Date().getFullYear();
    const randomNum = Math.floor(Math.random() * 900) + 100;

    const prefixes = {
        'installation': 'INST',
        'etancheite': 'ETAN',
        'garantie': 'GAR',
        'depose': 'DEP',
        'controle': 'CTRL'
    };

    const prefix = prefixes[type] || 'CERT';
    return `${prefix}-${randomNum}/${year}`;
}

function getCertificateTypeName(type) {
    const names = {
        'installation': 'التركيب',
        'etancheite': 'الختم',
        'garantie': 'الضمان',
        'depose': 'نزع الغاز',
        'controle': 'المراقبة'
    };

    return names[type] || 'الشهادة';
}

function createCertificateHTMLByType(type, customer, vehicle, gasTank, certificateNumber, currentDate) {
    const certificateTitle = getCertificateTitle(type);

    // تصميم خاص لشهادة الختم مطابق للصورة بالضبط
    if (type === 'etancheite') {
        return `
            <div class="certificate-container" style="width: 210mm; min-height: 297mm; margin: 0 auto; padding: 15mm; background: white; font-family: Arial, sans-serif; font-size: 11px;">
                <!-- رأس الشهادة مطابق للصورة -->
                <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 15px;">
                    <div style="text-align: right; font-size: 9px; line-height: 1.2; width: 30%;">
                        <div>وزارة الطاقة والمناجم</div>
                        <div style="margin-top: 5px;">N°440/2019</div>
                        <div>MINISTÈRE-ÉNERGIE-MINES</div>
                        <div>0696924176</div>
                        <div>P32</div>
                    </div>

                    <div style="text-align: center; font-size: 9px; line-height: 1.2; width: 40%;">
                        <div style="font-weight: bold; margin-bottom: 5px;">N°ETAN-841/2025</div>
                        <div>RÉPUBLIQUE ALGÉRIENNE DÉMOCRATIQUE ET POPULAIRE</div>
                        <div>MINISTÈRE DE L'ÉNERGIE ET DES MINES</div>
                        <div>DIRECTION DES HYDROCARBURES</div>
                        <div>SOUS-DIRECTION GAZ</div>
                    </div>

                    <div style="text-align: left; font-size: 9px; line-height: 1.2; width: 30%;">
                        <div>الجمهورية الجزائرية</div>
                        <div>وزارة الطاقة والمناجم</div>
                        <div>قسم المحروقات</div>
                        <div>المديرية الفرعية للغاز</div>
                    </div>
                </div>

                <!-- عنوان الشهادة -->
                <div style="text-align: center; margin: 25px 0;">
                    <h1 style="font-size: 18px; font-weight: bold; margin: 0; letter-spacing: 2px;">CERTIFICAT D'ÉTANCHÉITÉ</h1>
                </div>

                <!-- بيانات المركبة -->
                <div style="margin-bottom: 15px;">
                    <div style="font-weight: bold; margin-bottom: 8px; font-size: 12px;">VÉHICULE:</div>
                    <table style="width: 100%; border-collapse: collapse; font-size: 10px;">
                        <tr>
                            <td style="padding: 3px; width: 15%;">Immatriculation:</td>
                            <td style="border: 1px solid #000; padding: 3px 6px; width: 20%;">${vehicle.plateNumber || '00106-115-26'}</td>
                            <td style="padding: 3px; width: 15%;">Appartenant à:</td>
                            <td style="border: 1px solid #000; padding: 3px 6px; width: 50%;">${customer.name || ''}</td>
                        </tr>
                        <tr>
                            <td style="padding: 3px;">Genre:</td>
                            <td style="border: 1px solid #000; padding: 3px 6px;">${vehicle.model || 'VP'}</td>
                            <td style="padding: 3px;">Marque:</td>
                            <td style="border: 1px solid #000; padding: 3px 6px;">${vehicle.brand || 'DACIA'}</td>
                        </tr>
                        <tr>
                            <td style="padding: 3px;">Type:</td>
                            <td style="border: 1px solid #000; padding: 3px 6px;">${vehicle.type || '1014SDDAF4'}</td>
                            <td style="padding: 3px;">N° d'ordre:</td>
                            <td style="border: 1px solid #000; padding: 3px 6px;">${vehicle.orderNumber || '52396962'}</td>
                        </tr>
                    </table>
                </div>

                <!-- بيانات الخزان -->
                <div style="margin-bottom: 15px;">
                    <table style="width: 100%; border-collapse: collapse; font-size: 10px;">
                        <tr>
                            <td style="padding: 3px; width: 15%;">Marque:</td>
                            <td style="border: 1px solid #000; padding: 3px 6px; width: 20%;">${gasTank.brand || 'BRC A'}</td>
                            <td style="padding: 3px; width: 15%;">N° Réservoir:</td>
                            <td style="border: 1px solid #000; padding: 3px 6px; width: 20%;">${gasTank.serialNumber || '025778'}</td>
                        </tr>
                        <tr>
                            <td style="padding: 3px;">Capacité:</td>
                            <td style="border: 1px solid #000; padding: 3px 6px;">${gasTank.capacity || '60'} LITRES</td>
                            <td style="padding: 3px;">Année fabrication:</td>
                            <td style="border: 1px solid #000; padding: 3px 6px;">${gasTank.manufactureYear || '2016-04'}</td>
                        </tr>
                        <tr>
                            <td style="padding: 3px;">Date de 1re épreuve:</td>
                            <td style="border: 1px solid #000; padding: 3px 6px;">${formatDateFrench(gasTank.manufactureDate) || '2016-04-01'}</td>
                            <td style="padding: 3px;">Date de validité:</td>
                            <td style="border: 1px solid #000; padding: 3px 6px;">${formatDateFrench(new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0])}</td>
                        </tr>
                    </table>
                </div>

                <!-- مكتب المناجم -->
                <div style="margin: 20px 0;">
                    <div style="font-weight: bold; font-size: 12px; margin-bottom: 10px;">BUREAU DES MINES DE MÉDÉA :</div>
                    <div style="margin-bottom: 8px; font-size: 10px;"><strong>CONTROLE TECHNIQUE DU :</strong></div>
                    <div style="margin-bottom: 8px; font-size: 10px;">L'EXPERT Mr : ...................................................</div>
                    <div style="margin-bottom: 12px; font-size: 10px;">Cachet et signature : ...................................................</div>

                    <div style="text-align: justify; line-height: 1.3; margin-bottom: 8px; font-size: 10px;">
                        Test d'étanchéité à 10 bars sur l'installation GPL/GNC du véhicule réalisé avec succès.
                    </div>
                    <div style="text-align: justify; line-height: 1.3; margin-bottom: 8px; font-size: 10px;">
                        Véhicule à représenter au prochain contrôle technique avant le :
                    </div>
                    <div style="text-align: justify; line-height: 1.3; margin-bottom: 12px; font-size: 10px;">
                        Toute modification ou réparation intervenant sur une installation au G.P.L équipant un véhicule automobile doit faire l'objet d'un agrément conforme aux dispositions de l'arrêté du 13 avril 2001 relatif à l'installation et à l'utilisation du G.P.L carburant sur les véhicules Automobiles.
                    </div>
                </div>

                <!-- التعليمات الخاصة -->
                <div style="margin: 20px 0;">
                    <div style="font-weight: bold; font-size: 12px; margin-bottom: 8px;">CONSIGNES PARTICULIÈRES</div>
                    <div style="text-align: justify; line-height: 1.3; font-size: 9px;">
                        À l'issue de chaque chargement, il importe d'aérer suffisamment et efficacement aussi bien le coffre que l'habitacle. Durant cette opération les passagers et le conducteur devront s'abstenir d'utiliser ou de provoquer toute flamme ou étincelle pour éviter tout risque de feu.
                    </div>
                </div>

                <!-- التاريخ والمكان -->
                <div style="text-align: right; margin-top: 30px;">
                    <div style="font-weight: bold; font-size: 11px;">MÉDÉA Le : ${formatDateFrench(currentDate)}</div>
                </div>
            </div>
        `;
    }

    // للشهادات الأخرى - التصميم العادي
    const certificateContent = getCertificateContent(type, customer, vehicle, gasTank);
    return `
        <div class="certificate-container" style="width: 210mm; min-height: 297mm; margin: 0 auto; padding: 20mm; background: white; box-shadow: 0 0 10px rgba(0,0,0,0.1);">
            <!-- رأس الشهادة -->
            <div style="text-align: center; margin-bottom: 30px;">
                <div style="float: right; text-align: right; font-size: 12px; line-height: 1.4;">
                    <div>الجمهورية الجزائرية الديمقراطية الشعبية</div>
                    <div>وزارة الطاقة والمناجم</div>
                    <div>رقم الاعتماد:</div>
                    <div>${certificateNumber}</div>
                </div>
                <div style="float: left; text-align: left; font-size: 12px; line-height: 1.4;">
                    <div>République Algérienne Démocratique et Populaire</div>
                    <div>Ministère de l'Énergie et des Mines</div>
                    <div>N° d'agrément:</div>
                    <div>${certificateNumber}</div>
                </div>
                <div style="clear: both;"></div>
            </div>

            <!-- عنوان الشهادة -->
            <div style="text-align: center; margin: 40px 0;">
                <h1 style="font-size: 24px; font-weight: bold; margin: 0;">${certificateTitle}</h1>
            </div>

            ${certificateContent}

            <!-- التوقيع والتاريخ -->
            <div style="margin-top: 50px;">
                <div style="text-align: right;">
                    <div style="font-size: 16px; font-weight: bold; margin-bottom: 20px;">
                        <div>MÉDÉA Le : ${formatDateFrench(currentDate)}</div>
                    </div>
                    <div>الخبير / L'Expert: ${appData.settings.expertName || '...................................'}</div>
                    <div style="margin-top: 20px;">الختم والتوقيع: .....................................</div>
                </div>
            </div>
        </div>
    `;
}

function getCertificateTitle(type) {
    const titles = {
        'installation': 'CERTIFICAT D\'INSTALLATION',
        'etancheite': 'CERTIFICAT D\'ÉTANCHÉITÉ',
        'garantie': 'CERTIFICAT DE GARANTIE',
        'depose': 'CERTIFICAT DE DÉPOSE',
        'controle': 'CERTIFICAT DE CONTRÔLE'
    };

    return titles[type] || 'CERTIFICAT';
}

function getCertificateContent(type, customer, vehicle, gasTank) {
    const baseContent = `
        <!-- معلومات المركبة -->
        <div style="margin-bottom: 25px; border: 2px solid #000; padding: 15px;">
            <div style="background: #f0f0f0; padding: 8px; margin: -15px -15px 15px -15px; font-weight: bold; font-size: 16px;">
                VEHICULE:
            </div>
            <table style="width: 100%; border-collapse: collapse;">
                <tr>
                    <td style="padding: 8px; border: 1px solid #000; background: #f9f9f9; font-weight: bold; width: 25%;">Immatriculation:</td>
                    <td style="padding: 8px; border: 1px solid #000; width: 25%;">${vehicle.plateNumber || ''}</td>
                    <td style="padding: 8px; border: 1px solid #000; background: #f9f9f9; font-weight: bold; width: 25%;">Appartenant à:</td>
                    <td style="padding: 8px; border: 1px solid #000; width: 25%;">${customer.name || ''}</td>
                </tr>
                <tr>
                    <td style="padding: 8px; border: 1px solid #000; background: #f9f9f9; font-weight: bold;">Genre:</td>
                    <td style="padding: 8px; border: 1px solid #000;">${vehicle.model || 'VP'}</td>
                    <td style="padding: 8px; border: 1px solid #000; background: #f9f9f9; font-weight: bold;">Marque:</td>
                    <td style="padding: 8px; border: 1px solid #000;">${vehicle.brand || ''}</td>
                </tr>
                <tr>
                    <td style="padding: 8px; border: 1px solid #000; background: #f9f9f9; font-weight: bold;">N° d'ordre:</td>
                    <td style="padding: 8px; border: 1px solid #000;">${vehicle.orderNumber || ''}</td>
                    <td style="padding: 8px; border: 1px solid #000; background: #f9f9f9; font-weight: bold;">Année:</td>
                    <td style="padding: 8px; border: 1px solid #000;">${vehicle.year || ''}</td>
                </tr>
            </table>
        </div>

        <!-- معلومات الخزان -->
        <div style="margin-bottom: 25px; border: 2px solid #000; padding: 15px;">
            <div style="background: #f0f0f0; padding: 8px; margin: -15px -15px 15px -15px; font-weight: bold; font-size: 16px;">
                RESERVOIR:
            </div>
            <table style="width: 100%; border-collapse: collapse;">
                <tr>
                    <td style="padding: 8px; border: 1px solid #000; background: #f9f9f9; font-weight: bold; width: 25%;">Marque:</td>
                    <td style="padding: 8px; border: 1px solid #000; width: 25%;">${gasTank.brand || ''}</td>
                    <td style="padding: 8px; border: 1px solid #000; background: #f9f9f9; font-weight: bold; width: 25%;">N° Réservoir:</td>
                    <td style="padding: 8px; border: 1px solid #000; width: 25%;">${gasTank.serialNumber || ''}</td>
                </tr>
                <tr>
                    <td style="padding: 8px; border: 1px solid #000; background: #f9f9f9; font-weight: bold;">Capacité:</td>
                    <td style="padding: 8px; border: 1px solid #000;">${gasTank.capacity || ''} LITRES</td>
                    <td style="padding: 8px; border: 1px solid #000; background: #f9f9f9; font-weight: bold;">Année fabrication:</td>
                    <td style="padding: 8px; border: 1px solid #000;">${gasTank.manufactureYear || ''}</td>
                </tr>
                <tr>
                    <td style="padding: 8px; border: 1px solid #000; background: #f9f9f9; font-weight: bold;">Date de fabrication:</td>
                    <td style="padding: 8px; border: 1px solid #000;">${formatDate(gasTank.manufactureDate) || ''}</td>
                    <td style="padding: 8px; border: 1px solid #000; background: #f9f9f9; font-weight: bold;">Date de validité:</td>
                    <td style="padding: 8px; border: 1px solid #000;">${formatDate(new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0])}</td>
                </tr>
            </table>
        </div>
    `;

    const specificContent = getSpecificCertificateContent(type, customer, vehicle, gasTank);

    return baseContent + specificContent;
}

function getSpecificCertificateContent(type, customer, vehicle, gasTank) {
    switch (type) {
        case 'installation':
            return `
                <div style="margin: 20px 0; font-size: 14px;">
                    <p><strong>شهادة تركيب نظام الغاز الطبيعي المضغوط</strong></p>
                    <p>نشهد بأن نظام الغاز الطبيعي المضغوط قد تم تركيبه وفقاً للمعايير والمواصفات المعتمدة.</p>
                    <p>تاريخ التركيب: ${formatDate(new Date().toISOString().split('T')[0])}</p>
                </div>
            `;

        case 'etancheite':
            return `
                <div style="border: 2px solid #000; padding: 15px; margin: 20px 0;">
                    <div style="background: #f0f0f0; padding: 8px; margin: -15px -15px 15px -15px; font-weight: bold; font-size: 16px;">
                        BUREAU DES MINES DE MÉDÉA :
                    </div>
                    <div style="margin: 15px 0; font-size: 14px; line-height: 1.6;">
                        <p style="margin: 10px 0;"><strong>CONTROLE TECHNIQUE DU :</strong></p>
                        <p style="margin: 10px 0;">L'EXPERT Mr : ...................................................</p>
                        <p style="margin: 10px 0;">Cachet et signature : ...................................................</p>
                        <br>
                        <p style="margin: 10px 0; text-align: justify;">
                            Test d'étanchéité à 10 bars sur l'installation GPL/GNC du véhicule réalisé avec succès.
                        </p>
                        <p style="margin: 10px 0; text-align: justify;">
                            Véhicule à représenter au prochain contrôle technique avant le :
                        </p>
                        <p style="margin: 10px 0; text-align: justify;">
                            Toute modification ou réparation intervenant sur une installation au G.P.L équipant
                            un véhicule automobile doit faire l'objet d'un agrément conforme aux
                            dispositions de l'arrêté du 13 avril 2001 relatif à l'installation et à l'utilisation
                            du G.P.L carburant sur les véhicules Automobiles.
                        </p>
                    </div>
                </div>

                <div style="border: 2px solid #000; padding: 15px; margin: 20px 0;">
                    <div style="background: #f0f0f0; padding: 8px; margin: -15px -15px 15px -15px; font-weight: bold; font-size: 16px;">
                        CONSIGNES PARTICULIÈRES
                    </div>
                    <div style="margin: 15px 0; font-size: 12px; line-height: 1.5; text-align: justify;">
                        <p style="margin: 8px 0;">
                            À l'issue de chaque chargement, il importe d'éviter suffisamment et efficacement
                            aussi bien le coffre que l'habitacle. Durant cette opération les passagers et le
                            conducteur devront s'abstenir d'utiliser ou de provoquer toute flamme ou
                            étincelle pour éviter tout risque de feu.
                        </p>
                    </div>
                </div>
            `;

        case 'garantie':
            return `
                <div style="margin: 20px 0; font-size: 14px;">
                    <p><strong>شهادة الضمان</strong></p>
                    <p>نظام الغاز مضمون لمدة سنة واحدة من تاريخ التركيب.</p>
                    <p>الضمان يشمل جميع القطع والتركيبات.</p>
                </div>
            `;

        case 'depose':
            return `
                <div style="margin: 20px 0; font-size: 14px;">
                    <p><strong>شهادة نزع نظام الغاز</strong></p>
                    <p>تم نزع نظام الغاز الطبيعي المضغوط بالكامل.</p>
                    <p>تم التأكد من عدم وجود أي تسريبات أو مخاطر.</p>
                </div>
            `;

        case 'controle':
            return `
                <div style="margin: 20px 0; font-size: 14px;">
                    <p><strong>شهادة المراقبة الدورية</strong></p>
                    <p>تم فحص النظام والتأكد من سلامته وصلاحيته للاستخدام.</p>
                    <p>المراقبة القادمة: ${formatDate(new Date(Date.now() + 6 * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0])}</p>
                </div>
            `;

        default:
            return `
                <div style="margin: 20px 0; font-size: 14px;">
                    <p>تم إجراء الفحص والتأكد من مطابقة النظام للمعايير المطلوبة.</p>
                </div>
            `;
    }
}

// ===============================
// إدارة الوضع المظلم
// ===============================
function setupDarkMode() {
    const darkModeToggle = document.getElementById('dark-mode-toggle');
    if (!darkModeToggle) return;

    // تحميل الإعداد المحفوظ
    const isDarkMode = localStorage.getItem('darkMode') === 'true';
    if (isDarkMode) {
        document.body.classList.add('dark-mode');
        darkModeToggle.innerHTML = '<i class="fas fa-sun"></i>';
    }

    darkModeToggle.addEventListener('click', () => {
        document.body.classList.toggle('dark-mode');
        const isDark = document.body.classList.contains('dark-mode');

        darkModeToggle.innerHTML = isDark ? '<i class="fas fa-sun"></i>' : '<i class="fas fa-moon"></i>';
        localStorage.setItem('darkMode', isDark);
    });
}

// ===============================
// إدارة النوافذ المنبثقة
// ===============================
function setupModals() {
    // إغلاق النوافذ عند النقر على X أو خارج النافذة
    document.addEventListener('click', (e) => {
        if (e.target.classList.contains('close') || e.target.classList.contains('modal')) {
            const modal = e.target.closest('.modal') || e.target;
            if (modal.classList.contains('modal')) {
                modal.style.display = 'none';
            }
        }
    });

    // منع إغلاق النافذة عند النقر داخل المحتوى
    document.querySelectorAll('.modal-content').forEach(content => {
        content.addEventListener('click', (e) => {
            e.stopPropagation();
        });
    });
}

// ===============================
// إعداد أحداث Electron
// ===============================
function setupElectronEvents() {
    if (!window.electronAPI) return;

    // أحداث القائمة
    window.electronAPI.onMenuNew(() => {
        if (confirm('هل تريد إنشاء ملف جديد؟ سيتم فقدان البيانات غير المحفوظة.')) {
            appData = {
                gasCards: [],
                appointments: [],
                customers: [],
                vehicles: [],
                gasTanks: [],
                suppliers: [],
                inventory: [],
                sales: [],
                purchases: [],
                debts: [],
                certificates: [],
                settings: {
                    companyName: 'Future Fuel Corporation',
                    companyAddress: 'MEDEA, Algeria',
                    expertName: '',
                    issueLocation: 'MEDEA'
                }
            };
            updateDashboard();
            updateCustomersTable();
            updateCertificatesTable();
            showToast('تم إنشاء ملف جديد');
        }
    });

    window.electronAPI.onMenuSave(() => {
        saveData();
    });

    window.electronAPI.onMenuExport(async (event, filePath) => {
        try {
            const result = await window.electronAPI.exportData(filePath, appData);
            if (result.success) {
                showToast('تم تصدير البيانات بنجاح');
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            showToast('خطأ في تصدير البيانات', false);
        }
    });

    window.electronAPI.onMenuOpen(async (event, filePath) => {
        try {
            const result = await window.electronAPI.importData(filePath);
            if (result.success) {
                appData = { ...appData, ...result.data };
                updateDashboard();
                updateCustomersTable();
                updateCertificatesTable();
                showToast('تم استيراد البيانات بنجاح');
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            showToast('خطأ في استيراد البيانات', false);
        }
    });
}

// ===============================
// إعداد أزرار الإعدادات
// ===============================
function setupSettingsButtons() {
    const createBackupBtn = document.getElementById('create-backup-btn');
    if (createBackupBtn) {
        createBackupBtn.addEventListener('click', createBackup);
    }

    const exportDataBtn = document.getElementById('export-data-btn');
    if (exportDataBtn) {
        exportDataBtn.addEventListener('click', exportData);
    }

    const importDataBtn = document.getElementById('import-data-btn');
    if (importDataBtn) {
        importDataBtn.addEventListener('click', importData);
    }

    const restoreBackupBtn = document.getElementById('restore-backup-btn');
    if (restoreBackupBtn) {
        restoreBackupBtn.addEventListener('click', restoreBackup);
    }
}

// ===============================
// دوال مساعدة
// ===============================
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

function formatDate(dateString) {
    if (!dateString) return '-';
    return formatDateWithFrenchMonth(dateString);
}

function formatDateArabic(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    });
}

function formatDateFrench(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    const months = [
        'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
        'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
    ];

    const day = date.getDate().toString().padStart(2, '0');
    const month = months[date.getMonth()];
    const year = date.getFullYear();

    return `${day} ${month} ${year}`;
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function getStatusText(status) {
    const statusTexts = {
        'active': 'نشط',
        'expired': 'منتهي',
        'pending': 'معلق',
        'completed': 'مكتمل',
        'cancelled': 'ملغي',
        'paid': 'مدفوع',
        'unpaid': 'غير مدفوع'
    };
    return statusTexts[status] || status;
}

function getOperationTypeArabic(operationType) {
    const operationTypes = {
        'installation': 'تركيب',
        'monitoring': 'مراقبة',
        'etancheite': 'مراقبة',
        'controle': 'مراقبة',
        'garantie': 'ضمان',
        'depose': 'نزع'
    };
    return operationTypes[operationType] || operationType;
}

function editCustomer(customerId) {
    openCustomerModal(customerId);
}

function deleteCustomer(customerId) {
    if (confirm('هل أنت متأكد من حذف هذا الزبون؟')) {
        appData.customers = appData.customers.filter(c => c.id !== customerId);
        appData.vehicles = appData.vehicles.filter(v => v.customerId !== customerId);
        appData.gasTanks = appData.gasTanks.filter(t => {
            const vehicle = appData.vehicles.find(v => v.id === t.vehicleId);
            return !vehicle || vehicle.customerId !== customerId;
        });

        saveData();
        updateCustomersTable();
        updateDashboard();
        showToast('تم حذف الزبون بنجاح');
    }
}

// ===============================
// تحميل التطبيق
// ===============================
document.addEventListener('DOMContentLoaded', async () => {
    console.log('🚀 بدء تحميل التطبيق...');

    // تحميل البيانات
    await loadData();
    console.log('📊 تم تحميل البيانات');

    // تحديث التاريخ والوقت
    updateDateTime();
    setInterval(updateDateTime, 1000);

    // إعداد التنقل
    setupNavigation();

    // إعداد النماذج
    console.log('📝 إعداد النماذج...');
    setupCustomerForm();

    // إعداد النوافذ المنبثقة
    setupModals();

    // إعداد الوضع المظلم
    setupDarkMode();

    // إعداد أحداث Electron
    setupElectronEvents();

    // إعداد أزرار الإعدادات
    setupSettingsButtons();

    // تحديث لوحة التحكم
    updateDashboard();

    // تحديث جميع الجداول
    updateCustomersTable();
    updateCertificatesTable();

    // تحديث قائمة النسخ الاحتياطية
    updateBackupsList();

    // فحص تنبيه الإرسال عند التحميل
    checkTransmissionAlert();

    // إضافة مستمع للبحث المباشر في جدول الإرسال
    const transmissionSearch = document.getElementById('transmission-search');
    if (transmissionSearch) {
        transmissionSearch.addEventListener('input', searchTransmissionTable);
    }

    // تهيئة تاريخ العملية إلى اليوم
    const operationDateInput = document.getElementById('operation-date');
    if (operationDateInput) {
        operationDateInput.value = new Date().toISOString().split('T')[0];
    }
});

// ===== وظائف نوع العملية =====

// التعامل مع تغيير نوع العملية
function handleOperationTypeChange() {
    const operationType = document.getElementById('operation-type').value;
    const cardRenewalSection = document.getElementById('card-renewal-section');
    const certificateTypeSection = document.getElementById('certificate-type-section');

    // إخفاء جميع الأقسام أولاً
    if (cardRenewalSection) cardRenewalSection.style.display = 'none';
    if (certificateTypeSection) certificateTypeSection.style.display = 'none';

    // إظهار القسم المناسب حسب نوع العملية
    if (operationType === 'card-renewal') {
        if (cardRenewalSection) cardRenewalSection.style.display = 'block';
    } else if (operationType === 'installation' || operationType === 'monitoring') {
        if (certificateTypeSection) certificateTypeSection.style.display = 'block';

        // تحديد نوع الشهادة تلقائياً
        const certificateTypeSelect = document.getElementById('certificate-type');
        if (certificateTypeSelect) {
            if (operationType === 'installation') {
                certificateTypeSelect.value = 'installation';
            } else if (operationType === 'monitoring') {
                certificateTypeSelect.value = 'etancheite';
            }
        }
    }

    // تحديث تاريخ العملية إلى اليوم
    const operationDateInput = document.getElementById('operation-date');
    if (operationDateInput && !operationDateInput.value) {
        operationDateInput.value = new Date().toISOString().split('T')[0];
    }
}

// إضافة بطاقة غاز جديدة
function addGasCard(customerData, cardData) {
    console.log('🆔 إنشاء بطاقة غاز جديدة...');
    console.log('👤 بيانات الزبون:', customerData);
    console.log('🆔 بيانات البطاقة:', cardData);

    const gasCard = {
        id: generateId(),
        customerId: customerData.id,
        cardNumber: generateGasCardNumber(),
        customerName: customerData.name,
        registrationNumber: cardData.oldCardNumber || '',
        issueDate: new Date().toISOString().split('T')[0],
        expiryDate: calculateCardExpiryDate(),
        renewalReason: cardData.renewalReason || '',
        notes: cardData.notes || '',
        status: 'active',
        createdAt: new Date().toISOString()
    };

    console.log('🆔 البطاقة المُنشأة:', gasCard);

    // إضافة إلى قاعدة البيانات
    if (!appData.gasCards) {
        appData.gasCards = [];
    }
    appData.gasCards.push(gasCard);

    saveData();
    updateGasCardsTable(); // تحديث جدول البطاقات
    showToast('تم إضافة بطاقة الغاز بنجاح');

    return gasCard;
}

// توليد رقم بطاقة غاز
function generateGasCardNumber() {
    const year = new Date().getFullYear();
    const month = (new Date().getMonth() + 1).toString().padStart(2, '0');
    const cardCount = (appData.gasCards ? appData.gasCards.length : 0) + 1;
    const cardNumber = cardCount.toString().padStart(4, '0');

    return `GAS-${year}-${month}-${cardNumber}`;
}

// حساب تاريخ انتهاء البطاقة (سنة واحدة من تاريخ الإصدار)
function calculateCardExpiryDate() {
    const issueDate = new Date();
    const expiryDate = new Date(issueDate);
    expiryDate.setFullYear(expiryDate.getFullYear() + 1);

    return expiryDate.toISOString().split('T')[0];
}

// إنشاء سجل شهادة
function createCertificateRecord(customerData, vehicleData, tankData, certificateType) {
    const certificate = {
        id: generateId(),
        customerId: customerData.id,
        vehicleId: vehicleData.id,
        gasTankId: tankData.id,
        type: certificateType,
        certificateNumber: generateCertificateNumberByType(certificateType),
        issueDate: new Date().toISOString().split('T')[0],
        status: 'active',
        customerName: customerData.name,
        plateNumber: vehicleData.plateNumber,
        nextMonitoring: certificateType === 'controle' || certificateType === 'etancheite' ?
            new Date(Date.now() + 6 * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] : null
    };

    if (!appData.certificates) {
        appData.certificates = [];
    }

    appData.certificates.push(certificate);
    return certificate;
}

// ===== وظائف جدول الإرسال =====

// إضافة عملية إلى جدول الإرسال
function addToTransmissionTable(operationType, customerData, vehicleData, gasTankData) {
    if (operationType === 'تركيب' || operationType === 'مراقبة') {
        const transmissionEntry = {
            type: operationType,
            tankNumber: gasTankData.serialNumber || '',
            carType: vehicleData.model || '',
            serialNumber: gasTankData.serialNumber || '',
            registrationNumber: vehicleData.plateNumber || '',
            ownerName: customerData.name || '',
            operationDate: new Date().toISOString().split('T')[0],
            createdAt: new Date().toISOString()
        };

        // إضافة إلى جدول الإرسال
        if (!appData.transmissionTable) {
            appData.transmissionTable = [];
        }
        appData.transmissionTable.push(transmissionEntry);

        // حفظ البيانات
        saveData();

        // تحديث الجدول إذا كان مفتوحاً
        if (document.getElementById('transmission-table').style.display !== 'none') {
            updateTransmissionTable();
        }

        showToast(`تم إضافة عملية ${operationType} إلى جدول الإرسال`);
    }
}

// تحديث جدول الإرسال
function updateTransmissionTable() {
    const tableBody = document.getElementById('transmission-table-body');
    const emptyState = document.getElementById('transmission-empty-state');

    if (!appData.transmissionTable || appData.transmissionTable.length === 0) {
        tableBody.innerHTML = '';
        emptyState.style.display = 'block';
        updateTransmissionSummary();
        return;
    }

    // تطبيق الفلاتر
    let filteredData = [...appData.transmissionTable];

    // فلتر النوع
    const typeFilter = document.getElementById('transmission-type-filter');
    if (typeFilter && typeFilter.value !== 'all') {
        filteredData = filteredData.filter(entry => entry.type === typeFilter.value);
    }

    // فلتر الشهر
    const monthFilter = document.getElementById('transmission-month-filter');
    if (monthFilter && monthFilter.value !== 'all') {
        const now = new Date();
        const currentMonth = now.getMonth();
        const currentYear = now.getFullYear();

        if (monthFilter.value === 'current') {
            filteredData = filteredData.filter(entry => {
                const entryDate = new Date(entry.operationDate);
                return entryDate.getMonth() === currentMonth && entryDate.getFullYear() === currentYear;
            });
        } else if (monthFilter.value === 'last') {
            const lastMonth = currentMonth === 0 ? 11 : currentMonth - 1;
            const lastMonthYear = currentMonth === 0 ? currentYear - 1 : currentYear;
            filteredData = filteredData.filter(entry => {
                const entryDate = new Date(entry.operationDate);
                return entryDate.getMonth() === lastMonth && entryDate.getFullYear() === lastMonthYear;
            });
        }
    }

    // فلتر البحث
    const searchInput = document.getElementById('transmission-search');
    if (searchInput) {
        const searchTerm = searchInput.value.toLowerCase();
        if (searchTerm) {
            filteredData = filteredData.filter(entry =>
                entry.ownerName.toLowerCase().includes(searchTerm) ||
                entry.registrationNumber.toLowerCase().includes(searchTerm) ||
                entry.tankNumber.toLowerCase().includes(searchTerm) ||
                entry.carType.toLowerCase().includes(searchTerm)
            );
        }
    }

    if (filteredData.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="9" style="text-align: center; padding: 2rem; color: #6b7280;">لا توجد نتائج تطابق البحث</td></tr>';
        emptyState.style.display = 'none';
    } else {
        emptyState.style.display = 'none';

        // ترتيب البيانات حسب تاريخ العملية (الأحدث أولاً)
        filteredData.sort((a, b) => new Date(b.operationDate) - new Date(a.operationDate));

        tableBody.innerHTML = filteredData.map((entry, index) => `
            <tr>
                <td>${entry.type}</td>
                <td>${entry.tankNumber}</td>
                <td>${entry.carType}</td>
                <td>${entry.serialNumber}</td>
                <td>${entry.registrationNumber}</td>
                <td>${entry.ownerName}</td>
                <td>${index + 1}</td>
                <td>${formatDate(entry.operationDate)}</td>
                <td class="no-print">
                    <button onclick="deleteTransmissionEntry(${appData.transmissionTable.indexOf(entry)})" class="btn btn-sm btn-danger" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }

    updateTransmissionSummary();
}

// تحديث ملخص جدول الإرسال
function updateTransmissionSummary() {
    if (!appData.transmissionTable) {
        appData.transmissionTable = [];
    }

    const totalCount = appData.transmissionTable.length;
    const installationCount = appData.transmissionTable.filter(entry => entry.type === 'تركيب').length;
    const monitoringCount = appData.transmissionTable.filter(entry => entry.type === 'مراقبة').length;

    // عمليات الشهر الحالي
    const now = new Date();
    const currentMonth = now.getMonth();
    const currentYear = now.getFullYear();
    const currentMonthCount = appData.transmissionTable.filter(entry => {
        const entryDate = new Date(entry.operationDate);
        return entryDate.getMonth() === currentMonth && entryDate.getFullYear() === currentYear;
    }).length;

    const totalCountEl = document.getElementById('transmission-total-count');
    const installationCountEl = document.getElementById('transmission-installation-count');
    const monitoringCountEl = document.getElementById('transmission-monitoring-count');
    const currentMonthCountEl = document.getElementById('transmission-current-month-count');

    if (totalCountEl) totalCountEl.textContent = totalCount;
    if (installationCountEl) installationCountEl.textContent = installationCount;
    if (monitoringCountEl) monitoringCountEl.textContent = monitoringCount;
    if (currentMonthCountEl) currentMonthCountEl.textContent = currentMonthCount;
}

// حذف عملية من جدول الإرسال
function deleteTransmissionEntry(index) {
    if (confirm('هل أنت متأكد من حذف هذه العملية من جدول الإرسال؟')) {
        appData.transmissionTable.splice(index, 1);
        saveData();
        updateTransmissionTable();
        showToast('تم حذف العملية من جدول الإرسال');
    }
}

// البحث في جدول الإرسال
function searchTransmissionTable() {
    updateTransmissionTable();
}

// تصفية جدول الإرسال
function filterTransmissionTable() {
    updateTransmissionTable();
}

// طباعة جدول الإرسال
function printTransmissionTable() {
    // إخفاء العناصر غير المطلوبة للطباعة
    const elementsToHide = document.querySelectorAll('.no-print, .transmission-controls, .transmission-filters, .transmission-summary');
    elementsToHide.forEach(el => el.style.display = 'none');

    // طباعة
    window.print();

    // إعادة إظهار العناصر
    elementsToHide.forEach(el => el.style.display = '');
}

// تصدير جدول الإرسال إلى PDF
function exportTransmissionToPDF() {
    showToast('جاري تصدير جدول الإرسال إلى PDF...');

    // محاكاة عملية التصدير
    setTimeout(() => {
        showToast('تم تصدير جدول الإرسال إلى PDF بنجاح');
    }, 2000);
}

// مسح جدول الإرسال
function clearTransmissionTable() {
    if (confirm('هل أنت متأكد من مسح جميع بيانات جدول الإرسال؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        if (confirm('تأكيد أخير: سيتم حذف جميع العمليات نهائياً. هل تريد المتابعة؟')) {
            appData.transmissionTable = [];
            saveData();
            updateTransmissionTable();
            showToast('تم مسح جدول الإرسال بنجاح');
        }
    }
}

// تحديث معلومات الشركة في جدول الإرسال
function updateTransmissionCompanyInfo() {
    const settings = appData.settings || {};

    // تحديث معلومات الشركة
    const companyNameEl = document.getElementById('company-name-display');
    const companyLicenseEl = document.getElementById('company-license-display');
    const companyWilayaEl = document.getElementById('company-wilaya-display');
    const companyWilayaFrEl = document.getElementById('company-wilaya-display-fr');
    const ownerNameEl = document.getElementById('owner-name-display');
    const companyPhoneEl = document.getElementById('company-phone-display');
    const currentMonthYearEl = document.getElementById('current-month-year');
    const currentMonthYearFrEl = document.getElementById('current-month-year-fr');

    if (companyNameEl) {
        const displayName = currentLanguage === 'fr' && settings.companyNameFr ?
            settings.companyNameFr : (settings.companyName || 'مركز وقود المستقبل');
        companyNameEl.textContent = displayName;
    }

    if (companyLicenseEl) companyLicenseEl.textContent = settings.licenseNumber || '463/2019';
    if (companyWilayaEl) companyWilayaEl.textContent = settings.companyWilaya || 'المدية';
    if (companyWilayaFrEl) {
        // تحويل اسم الولاية إلى الفرنسية
        const wilayaFr = getWilayaFrenchName(settings.companyWilaya || 'المدية');
        companyWilayaFrEl.textContent = wilayaFr;
    }
    if (ownerNameEl) ownerNameEl.textContent = settings.ownerName || '';
    if (companyPhoneEl) companyPhoneEl.textContent = settings.companyPhone || '';

    // تحديث الشهر والسنة الحالية بالفرنسية
    const now = new Date();
    const currentMonthYear = `${frenchMonths[now.getMonth()]} ${now.getFullYear()}`;

    if (currentMonthYearEl) currentMonthYearEl.textContent = currentMonthYear;
    if (currentMonthYearFrEl) currentMonthYearFrEl.textContent = currentMonthYear;
}

// تحويل اسم الولاية إلى الفرنسية
function getWilayaFrenchName(arabicName) {
    const wilayaMap = {
        'أدرار': 'Adrar',
        'الشلف': 'Chlef',
        'الأغواط': 'Laghouat',
        'أم البواقي': 'Oum El Bouaghi',
        'باتنة': 'Batna',
        'بجاية': 'Béjaïa',
        'بسكرة': 'Biskra',
        'بشار': 'Béchar',
        'البليدة': 'Blida',
        'البويرة': 'Bouira',
        'تمنراست': 'Tamanrasset',
        'تبسة': 'Tébessa',
        'تلمسان': 'Tlemcen',
        'تيارت': 'Tiaret',
        'تيزي وزو': 'Tizi Ouzou',
        'الجزائر': 'Alger',
        'الجلفة': 'Djelfa',
        'جيجل': 'Jijel',
        'سطيف': 'Sétif',
        'سعيدة': 'Saïda',
        'سكيكدة': 'Skikda',
        'سيدي بلعباس': 'Sidi Bel Abbès',
        'عنابة': 'Annaba',
        'قالمة': 'Guelma',
        'قسنطينة': 'Constantine',
        'المدية': 'Médéa',
        'مستغانم': 'Mostaganem',
        'المسيلة': 'M\'Sila',
        'معسكر': 'Mascara',
        'ورقلة': 'Ouargla',
        'وهران': 'Oran',
        'البيض': 'El Bayadh',
        'إليزي': 'Illizi',
        'برج بوعريريج': 'Bordj Bou Arréridj',
        'بومرداس': 'Boumerdès',
        'الطارف': 'El Tarf',
        'تندوف': 'Tindouf',
        'تيسمسيلت': 'Tissemsilt',
        'الوادي': 'El Oued',
        'خنشلة': 'Khenchela',
        'سوق أهراس': 'Souk Ahras',
        'تيبازة': 'Tipaza',
        'ميلة': 'Mila',
        'عين الدفلى': 'Aïn Defla',
        'النعامة': 'Naâma',
        'عين تموشنت': 'Aïn Témouchent',
        'غرداية': 'Ghardaïa',
        'غليزان': 'Relizane'
    };

    return wilayaMap[arabicName] || arabicName;
}

// فحص تنبيه الإرسال (كل 6 أشهر)
function checkTransmissionAlert() {
    const settings = appData.settings || {};
    const lastSentDate = settings.lastTransmissionSent;

    if (!lastSentDate) {
        // إذا لم يتم الإرسال من قبل، اعتبر أن آخر إرسال كان منذ 6 أشهر
        const sixMonthsAgo = new Date();
        sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
        settings.lastTransmissionSent = sixMonthsAgo.toISOString();
        saveData();
    }

    const lastSent = new Date(lastSentDate || new Date());
    const now = new Date();
    const sixMonthsInMs = 6 * 30 * 24 * 60 * 60 * 1000; // تقريبي

    const alertEl = document.getElementById('transmission-alert');
    if (alertEl) {
        if (now - lastSent > sixMonthsInMs) {
            alertEl.style.display = 'flex';
        } else {
            alertEl.style.display = 'none';
        }
    }
}

// تسجيل أنه تم الإرسال
function markAsSent() {
    if (!appData.settings) {
        appData.settings = {};
    }
    appData.settings.lastTransmissionSent = new Date().toISOString();
    saveData();

    const alertEl = document.getElementById('transmission-alert');
    if (alertEl) {
        alertEl.style.display = 'none';
    }
    showToast('تم تسجيل إرسال الجدول بنجاح');
}

// ===============================
// وظائف اللغة الإضافية
// ===============================

// تحديث أزرار اللغة
function updateLanguageButtons() {
    const arBtn = document.getElementById('lang-ar');
    const frBtn = document.getElementById('lang-fr');

    if (arBtn && frBtn) {
        arBtn.classList.toggle('active', currentLanguage === 'ar');
        frBtn.classList.toggle('active', currentLanguage === 'fr');
    }
}

// تحديث نصوص الأزرار
function updateButtonTexts() {
    // تحديث أزرار الإجراءات
    const buttons = {
        'add-customer-btn': 'addNewCustomer',
        'print-customers-btn': 'print',
        'add-card-btn': 'add',
        'print-cards-btn': 'print'
    };

    Object.entries(buttons).forEach(([id, key]) => {
        const element = document.getElementById(id);
        if (element) {
            const icon = element.querySelector('i');
            const iconClass = icon ? icon.outerHTML : '';
            element.innerHTML = `${iconClass} ${t(key)}`;
        }
    });
}

// تحديث نصوص النماذج
function updateFormTexts() {
    // تحديث عناوين النماذج
    const modalTitle = document.getElementById('customer-modal-title');
    if (modalTitle) {
        modalTitle.textContent = t('addNewCustomer');
    }

    // تحديث تسميات الحقول
    const labels = {
        'customer-name': 'customerName',
        'customer-phone': 'customerPhone',
        'customer-email': 'customerEmail',
        'customer-address': 'customerAddress',
        'operation-type': 'operationType'
    };

    Object.entries(labels).forEach(([id, key]) => {
        const element = document.querySelector(`label[for="${id}"]`);
        if (element) {
            const required = element.querySelector('.required');
            const requiredHtml = required ? required.outerHTML : '';
            element.innerHTML = `${t(key)}: ${requiredHtml}`;
        }
    });
}

// تحديث التاريخ والوقت بالأشهر الفرنسية
function updateDateTime() {
    const now = new Date();

    // تحديث التاريخ
    const dateEl = document.getElementById('current-date');
    if (dateEl) {
        dateEl.textContent = formatDateWithFrenchMonth(now.toISOString().split('T')[0]);
    }

    // تحديث الوقت
    const timeEl = document.getElementById('current-time');
    if (timeEl) {
        timeEl.textContent = now.toLocaleTimeString('fr-FR', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }
}

// تحديث الشهر والسنة في جدول الإرسال
function updateTransmissionMonthYear() {
    const currentMonthYearEl = document.getElementById('current-month-year');
    if (currentMonthYearEl) {
        const now = new Date();
        const monthYear = `${frenchMonths[now.getMonth()]} ${now.getFullYear()}`;
        currentMonthYearEl.textContent = monthYear;
    }
}

// تحديث دوري للتاريخ والوقت
setInterval(updateDateTime, 1000);

// تحديث التاريخ والوقت عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    updateDateTime();
    updateTransmissionMonthYear();
    setupCompanySettings();
    setupReminderSettings();
});

// ===============================
// إعدادات الشركة
// ===============================

// إعداد نموذج إعدادات الشركة
function setupCompanySettings() {
    const form = document.getElementById('company-settings-form');
    if (form) {
        form.addEventListener('submit', saveCompanySettings);
        loadCompanySettings();
    }

    const resetBtn = document.getElementById('reset-company-settings');
    if (resetBtn) {
        resetBtn.addEventListener('click', resetCompanySettings);
    }
}

// حفظ إعدادات الشركة
function saveCompanySettings(e) {
    e.preventDefault();

    const settings = {
        companyName: document.getElementById('company-name').value,
        companyNameFr: document.getElementById('company-name-fr').value,
        ownerName: document.getElementById('owner-name').value,
        licenseNumber: document.getElementById('license-number').value,
        companyPhone: document.getElementById('company-phone').value,
        companyFax: document.getElementById('company-fax').value,
        companyWilaya: document.getElementById('company-wilaya').value,
        companyAddress: document.getElementById('company-address').value,
        expertName: document.getElementById('expert-name').value,
        issueLocation: document.getElementById('issue-location').value
    };

    // التحقق من الحقول المطلوبة
    if (!settings.companyName || !settings.ownerName || !settings.licenseNumber ||
        !settings.companyPhone || !settings.companyWilaya) {
        showToast('يرجى ملء جميع الحقول المطلوبة', false);
        return;
    }

    // حفظ الإعدادات
    appData.settings = { ...appData.settings, ...settings };
    saveData();

    // تحديث المعلومات في جدول الإرسال والشهادات
    updateTransmissionCompanyInfo();
    updateCertificatesCompanyInfo();

    showToast('تم حفظ إعدادات الشركة بنجاح');
}

// تحميل إعدادات الشركة
function loadCompanySettings() {
    const settings = appData.settings || {};

    const fields = [
        'company-name', 'company-name-fr', 'owner-name', 'license-number',
        'company-phone', 'company-fax', 'company-wilaya', 'company-address',
        'expert-name', 'issue-location'
    ];

    fields.forEach(fieldId => {
        const element = document.getElementById(fieldId);
        const settingKey = fieldId.replace(/-([a-z])/g, (g) => g[1].toUpperCase());
        if (element && settings[settingKey]) {
            element.value = settings[settingKey];
        }
    });
}

// إعادة تعيين إعدادات الشركة
function resetCompanySettings() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع إعدادات الشركة؟')) {
        // القيم الافتراضية
        const defaultSettings = {
            companyName: 'مركز وقود المستقبل',
            companyNameFr: 'Future Fuel Corporation',
            ownerName: '',
            licenseNumber: '463/2019',
            companyPhone: '',
            companyFax: '',
            companyWilaya: 'المدية',
            companyAddress: '',
            expertName: '',
            issueLocation: 'المدية'
        };

        // تطبيق القيم الافتراضية
        Object.entries(defaultSettings).forEach(([key, value]) => {
            const fieldId = key.replace(/([A-Z])/g, '-$1').toLowerCase();
            const element = document.getElementById(fieldId);
            if (element) {
                element.value = value;
            }
        });

        showToast('تم إعادة تعيين الإعدادات إلى القيم الافتراضية');
    }
}

// تحديث معلومات الشركة في الشهادات
function updateCertificatesCompanyInfo() {
    // سيتم تطبيق هذا عند إنشاء الشهادات
    console.log('تم تحديث معلومات الشركة للشهادات');
}

// ===============================
// إدارة الديون
// ===============================

// تنسيق عرض مبلغ الدين
function formatDebtAmount(amount) {
    const debt = parseFloat(amount) || 0;

    if (debt === 0) {
        return '<span class="debt-amount no-debt">لا يوجد دين</span>';
    } else if (debt > 0 && debt <= 1000) {
        return `<span class="debt-amount has-debt">${formatNumberFrench(debt)} دج</span>`;
    } else {
        return `<span class="debt-amount high-debt">${formatNumberFrench(debt)} دج</span>`;
    }
}

// إدارة دين الزبون
function manageCustomerDebt(customerId) {
    const customer = appData.customers.find(c => c.id === customerId);
    if (!customer) return;

    const currentDebt = customer.debt || 0;
    const action = prompt(`إدارة دين الزبون: ${customer.name}\n\nالدين الحالي: ${formatNumberFrench(currentDebt)} دج\n\nاختر العملية:\n1 - تحديث مبلغ الدين\n2 - إضافة دين جديد\n3 - سداد جزئي\n4 - سداد كامل\n\nأدخل رقم العملية (1-4):`);

    if (!action) return;

    switch (action) {
        case '1':
            updateDebtAmount(customer);
            break;
        case '2':
            addNewDebt(customer);
            break;
        case '3':
            partialPayment(customer);
            break;
        case '4':
            fullPayment(customer);
            break;
        default:
            showToast('عملية غير صحيحة', false);
    }
}

// تحديث مبلغ الدين
function updateDebtAmount(customer) {
    const newAmount = prompt(`تحديث مبلغ الدين للزبون: ${customer.name}\n\nالدين الحالي: ${formatNumberFrench(customer.debt || 0)} دج\n\nأدخل المبلغ الجديد:`);

    if (newAmount === null) return;

    const amount = parseFloat(newAmount);
    if (isNaN(amount) || amount < 0) {
        showToast('يرجى إدخال مبلغ صحيح', false);
        return;
    }

    customer.debt = amount;
    saveData();
    updateCustomersTable();
    updateDashboard();
    showToast(`تم تحديث دين الزبون إلى ${formatNumberFrench(amount)} دج`);
}

// إضافة دين جديد
function addNewDebt(customer) {
    const additionalAmount = prompt(`إضافة دين جديد للزبون: ${customer.name}\n\nالدين الحالي: ${formatNumberFrench(customer.debt || 0)} دج\n\nأدخل المبلغ المراد إضافته:`);

    if (additionalAmount === null) return;

    const amount = parseFloat(additionalAmount);
    if (isNaN(amount) || amount <= 0) {
        showToast('يرجى إدخال مبلغ صحيح', false);
        return;
    }

    customer.debt = (customer.debt || 0) + amount;
    saveData();
    updateCustomersTable();
    updateDashboard();
    showToast(`تم إضافة ${formatNumberFrench(amount)} دج. إجمالي الدين: ${formatNumberFrench(customer.debt)} دج`);
}

// سداد جزئي
function partialPayment(customer) {
    const currentDebt = customer.debt || 0;
    if (currentDebt === 0) {
        showToast('لا يوجد دين للزبون', false);
        return;
    }

    const paymentAmount = prompt(`سداد جزئي للزبون: ${customer.name}\n\nالدين الحالي: ${formatNumberFrench(currentDebt)} دج\n\nأدخل مبلغ السداد:`);

    if (paymentAmount === null) return;

    const amount = parseFloat(paymentAmount);
    if (isNaN(amount) || amount <= 0) {
        showToast('يرجى إدخال مبلغ صحيح', false);
        return;
    }

    if (amount > currentDebt) {
        showToast('مبلغ السداد أكبر من الدين المتبقي', false);
        return;
    }

    customer.debt = currentDebt - amount;
    saveData();
    updateCustomersTable();
    updateDashboard();

    if (customer.debt === 0) {
        showToast(`تم سداد ${formatNumberFrench(amount)} دج. تم سداد الدين بالكامل!`);
    } else {
        showToast(`تم سداد ${formatNumberFrench(amount)} دج. الدين المتبقي: ${formatNumberFrench(customer.debt)} دج`);
    }
}

// سداد كامل
function fullPayment(customer) {
    const currentDebt = customer.debt || 0;
    if (currentDebt === 0) {
        showToast('لا يوجد دين للزبون', false);
        return;
    }

    if (confirm(`سداد كامل للزبون: ${customer.name}\n\nالدين الحالي: ${formatNumberFrench(currentDebt)} دج\n\nهل تريد تسجيل السداد الكامل؟`)) {
        customer.debt = 0;
        saveData();
        updateCustomersTable();
        updateDashboard();
        showToast(`تم سداد الدين بالكامل (${formatNumberFrench(currentDebt)} دج)`);
    }
}

// ===============================
// إعدادات التذكيرات
// ===============================

// إعداد نموذج إعدادات التذكيرات
function setupReminderSettings() {
    const form = document.getElementById('reminder-settings-form');
    if (form) {
        form.addEventListener('submit', saveReminderSettings);
        loadReminderSettings();
        setupReminderPreview();
    }

    const resetBtn = document.getElementById('reset-reminder-settings');
    if (resetBtn) {
        resetBtn.addEventListener('click', resetReminderSettings);
    }

    const testBtn = document.getElementById('test-reminders');
    if (testBtn) {
        testBtn.addEventListener('click', testReminders);
    }
}

// إعداد معاينة التذكيرات
function setupReminderPreview() {
    const inputs = ['card-reminder-days', 'debt-reminder-days', 'monitoring-reminder-months'];

    inputs.forEach(inputId => {
        const input = document.getElementById(inputId);
        if (input) {
            input.addEventListener('input', updateReminderPreview);
        }
    });
}

// تحديث معاينة التذكيرات
function updateReminderPreview() {
    const cardDays = document.getElementById('card-reminder-days').value || 30;
    const debtDays = document.getElementById('debt-reminder-days').value || 7;
    const monitoringMonths = document.getElementById('monitoring-reminder-months').value || 6;

    document.getElementById('preview-card-days').textContent = cardDays;
    document.getElementById('preview-debt-days').textContent = debtDays;
    document.getElementById('preview-monitoring-months').textContent = monitoringMonths;
}

// حفظ إعدادات التذكيرات
function saveReminderSettings(e) {
    e.preventDefault();

    const settings = {
        cardReminderDays: parseInt(document.getElementById('card-reminder-days').value) || 30,
        debtReminderDays: parseInt(document.getElementById('debt-reminder-days').value) || 7,
        highDebtThreshold: parseFloat(document.getElementById('high-debt-threshold').value) || 5000,
        monitoringReminderMonths: parseInt(document.getElementById('monitoring-reminder-months').value) || 6,
        enableSoundAlerts: document.getElementById('enable-sound-alerts').checked,
        enablePopupAlerts: document.getElementById('enable-popup-alerts').checked
    };

    // التحقق من القيم
    if (settings.cardReminderDays < 1 || settings.cardReminderDays > 365) {
        showToast('أيام تذكير البطاقة يجب أن تكون بين 1 و 365', false);
        return;
    }

    if (settings.debtReminderDays < 1 || settings.debtReminderDays > 365) {
        showToast('أيام تذكير الدين يجب أن تكون بين 1 و 365', false);
        return;
    }

    // حفظ الإعدادات
    appData.settings.reminders = settings;
    saveData();

    // تحديث التنبيهات
    updateAlerts();

    showToast('تم حفظ إعدادات التذكيرات بنجاح');
}

// تحميل إعدادات التذكيرات
function loadReminderSettings() {
    const reminders = appData.settings.reminders || {};

    document.getElementById('card-reminder-days').value = reminders.cardReminderDays || 30;
    document.getElementById('debt-reminder-days').value = reminders.debtReminderDays || 7;
    document.getElementById('high-debt-threshold').value = reminders.highDebtThreshold || 5000;
    document.getElementById('monitoring-reminder-months').value = reminders.monitoringReminderMonths || 6;
    document.getElementById('enable-sound-alerts').checked = reminders.enableSoundAlerts !== false;
    document.getElementById('enable-popup-alerts').checked = reminders.enablePopupAlerts !== false;

    updateReminderPreview();
}

// إعادة تعيين إعدادات التذكيرات
function resetReminderSettings() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع إعدادات التذكيرات؟')) {
        document.getElementById('card-reminder-days').value = 30;
        document.getElementById('debt-reminder-days').value = 7;
        document.getElementById('high-debt-threshold').value = 5000;
        document.getElementById('monitoring-reminder-months').value = 6;
        document.getElementById('enable-sound-alerts').checked = true;
        document.getElementById('enable-popup-alerts').checked = true;

        updateReminderPreview();
        showToast('تم إعادة تعيين إعدادات التذكيرات إلى القيم الافتراضية');
    }
}

// اختبار التذكيرات
function testReminders() {
    const settings = appData.settings.reminders || {};

    // إنشاء تذكير تجريبي
    showReminderAlert({
        type: 'card',
        title: 'تذكير تجريبي - انتهاء البطاقة',
        message: `هذا تذكير تجريبي. ستظهر التذكيرات قبل ${settings.cardReminderDays || 30} يوم من انتهاء البطاقة.`,
        urgent: false
    });

    setTimeout(() => {
        showReminderAlert({
            type: 'debt',
            title: 'تذكير تجريبي - استحقاق الدين',
            message: `هذا تذكير تجريبي. ستظهر التذكيرات قبل ${settings.debtReminderDays || 7} أيام من استحقاق الدين.`,
            urgent: true
        });
    }, 2000);

    showToast('تم إرسال تذكيرات تجريبية');
}

// عرض تذكير منبثق
function showReminderAlert(alert) {
    const settings = appData.settings.reminders || {};

    if (!settings.enablePopupAlerts) return;

    // إنشاء عنصر التذكير
    const alertElement = document.createElement('div');
    alertElement.className = `reminder-alert ${alert.urgent ? 'urgent' : ''}`;

    alertElement.innerHTML = `
        <div class="alert-header">
            <i class="fas fa-bell"></i>
            <span>${alert.title}</span>
            <button onclick="this.parentElement.parentElement.remove()" style="margin-right: auto; background: none; border: none; font-size: 1.2rem; cursor: pointer;">&times;</button>
        </div>
        <div class="alert-message">${alert.message}</div>
    `;

    // إضافة التذكير للصفحة
    document.body.appendChild(alertElement);

    // تشغيل الصوت إذا كان مفعلاً
    if (settings.enableSoundAlerts) {
        playNotificationSound();
    }

    // إزالة التذكير تلقائياً بعد 10 ثوان
    setTimeout(() => {
        if (alertElement.parentElement) {
            alertElement.remove();
        }
    }, 10000);
}

// تشغيل صوت التنبيه
function playNotificationSound() {
    try {
        // إنشاء صوت تنبيه بسيط
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
        oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);

        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);

        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.5);
    } catch (error) {
        console.log('لا يمكن تشغيل الصوت:', error);
    }
}

// فحص التذكيرات الدورية
function checkReminders() {
    const settings = appData.settings.reminders || {};
    const today = new Date();

    // فحص بطاقات تحتاج للتجديد
    checkCardReminders(settings, today);

    // فحص الديون المستحقة
    checkDebtReminders(settings, today);

    // فحص المراقبة الدورية
    checkMonitoringReminders(settings, today);
}

// فحص تذكيرات البطاقات
function checkCardReminders(settings, today) {
    const reminderDays = settings.cardReminderDays || 30;

    const expiringCards = appData.gasCards.filter(card => {
        const expiryDate = new Date(card.expiryDate);
        const diffTime = expiryDate - today;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return diffDays <= reminderDays && diffDays > 0;
    });

    if (expiringCards.length > 0) {
        const urgentCards = expiringCards.filter(card => {
            const expiryDate = new Date(card.expiryDate);
            const diffTime = expiryDate - today;
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            return diffDays <= 7;
        });

        showReminderAlert({
            type: 'card',
            title: 'تذكير انتهاء البطاقات',
            message: `${expiringCards.length} بطاقة تحتاج للتجديد خلال ${reminderDays} يوم${urgentCards.length > 0 ? ` (${urgentCards.length} بطاقة عاجلة)` : ''}`,
            urgent: urgentCards.length > 0
        });
    }
}

// فحص تذكيرات الديون
function checkDebtReminders(settings, today) {
    const reminderDays = settings.debtReminderDays || 7;
    const highDebtThreshold = settings.highDebtThreshold || 5000;

    const customersWithDebts = appData.customers.filter(customer => (customer.debt || 0) > 0);
    const highDebtCustomers = customersWithDebts.filter(customer => customer.debt >= highDebtThreshold);

    if (customersWithDebts.length > 0) {
        showReminderAlert({
            type: 'debt',
            title: 'تذكير الديون المستحقة',
            message: `${customersWithDebts.length} زبون لديهم ديون مستحقة${highDebtCustomers.length > 0 ? ` (${highDebtCustomers.length} دين عالي)` : ''}`,
            urgent: highDebtCustomers.length > 0
        });
    }
}

// فحص تذكيرات المراقبة الدورية
function checkMonitoringReminders(settings, today) {
    const reminderMonths = settings.monitoringReminderMonths || 6;

    // البحث عن المركبات التي تحتاج مراقبة دورية
    const vehiclesNeedingMonitoring = appData.vehicles.filter(vehicle => {
        const customer = appData.customers.find(c => c.id === vehicle.customerId);
        if (!customer) return false;

        const registrationDate = new Date(customer.registrationDate);
        const monthsSinceRegistration = (today - registrationDate) / (1000 * 60 * 60 * 24 * 30);

        return monthsSinceRegistration >= reminderMonths;
    });

    if (vehiclesNeedingMonitoring.length > 0) {
        showReminderAlert({
            type: 'monitoring',
            title: 'تذكير المراقبة الدورية',
            message: `${vehiclesNeedingMonitoring.length} مركبة تحتاج للمراقبة الدورية`,
            urgent: false
        });
    }
}

// تشغيل فحص التذكيرات كل ساعة
setInterval(checkReminders, 60 * 60 * 1000);

// فحص التذكيرات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 تحميل التطبيق...');

    setTimeout(checkReminders, 5000); // بعد 5 ثوان من التحميل
    setupCertificatesSystem();
    setupSuppliersSystem();

    // إعداد نماذج الزبائن
    console.log('⚙️ إعداد نماذج الزبائن...');
    setupCustomerForm();

    // إعداد نماذج بطاقات الغاز
    console.log('⚙️ إعداد نماذج بطاقات الغاز...');
    setupGasCardsForm();

    // إعداد نماذج المواعيد
    console.log('⚙️ إعداد نماذج المواعيد...');
    setupAppointmentsForm();

    // إضافة دالة اختبار مؤقتة
    window.testCustomerSave = function() {
        console.log('🧪 اختبار حفظ الزبون...');

        // ملء البيانات التجريبية
        const nameField = document.getElementById('customer-name');
        const phoneField = document.getElementById('customer-phone');
        const addressField = document.getElementById('customer-address');

        if (nameField) nameField.value = 'زبون تجريبي';
        if (phoneField) phoneField.value = '0555123456';
        if (addressField) addressField.value = 'عنوان تجريبي';

        // محاولة الحفظ
        saveCustomer();
    };

    window.checkCustomerForm = function() {
        console.log('🔍 فحص حالة نموذج الزبون...');

        const form = document.getElementById('customer-form');
        const modal = document.getElementById('customer-modal');
        const nameField = document.getElementById('customer-name');
        const phoneField = document.getElementById('customer-phone');
        const addressField = document.getElementById('customer-address');
        const submitBtn = form?.querySelector('button[type="submit"]');

        console.log('📋 حالة العناصر:', {
            form: !!form,
            modal: !!modal,
            modalVisible: modal?.style.display === 'block',
            nameField: !!nameField,
            phoneField: !!phoneField,
            addressField: !!addressField,
            submitBtn: !!submitBtn,
            formListeners: form?._listeners || 'غير متاح'
        });

        if (form) {
            console.log('📝 معلومات النموذج:', {
                id: form.id,
                action: form.action,
                method: form.method,
                onsubmit: form.onsubmit
            });
        }
    };

    // تشخيص شامل للنماذج
    setTimeout(() => {
        console.log('🔍 بدء التشخيص الشامل...');

        // فحص نموذج الزبون
        const customerForm = document.getElementById('customer-form');
        const customerModal = document.getElementById('customer-modal');
        const addCustomerBtn = document.getElementById('add-customer-btn');

        console.log('📋 تشخيص نموذج الزبون:', {
            form: !!customerForm,
            modal: !!customerModal,
            addBtn: !!addCustomerBtn,
            formId: customerForm?.id,
            modalId: customerModal?.id,
            btnId: addCustomerBtn?.id
        });

        if (customerForm) {
            const submitBtn = customerForm.querySelector('button[type="submit"]');
            const nameField = customerForm.querySelector('#customer-name');
            const phoneField = customerForm.querySelector('#customer-phone');
            const addressField = customerForm.querySelector('#customer-address');

            console.log('📝 عناصر النموذج:', {
                submitBtn: !!submitBtn,
                nameField: !!nameField,
                phoneField: !!phoneField,
                addressField: !!addressField,
                submitBtnText: submitBtn?.textContent,
                submitBtnType: submitBtn?.type
            });
        }

        // اختبار فتح النموذج
        if (addCustomerBtn) {
            console.log('🧪 اختبار فتح النموذج...');
            addCustomerBtn.click();

            setTimeout(() => {
                const isModalVisible = customerModal?.style.display === 'block';
                console.log('👁️ حالة النافذة بعد النقر:', {
                    modalVisible: isModalVisible,
                    modalDisplay: customerModal?.style.display
                });

                if (isModalVisible && customerForm) {
                    const submitBtn = customerForm.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        console.log('🎯 اختبار زر الحفظ...');

                        // ملء بيانات تجريبية
                        const nameField = document.getElementById('customer-name');
                        const phoneField = document.getElementById('customer-phone');
                        const addressField = document.getElementById('customer-address');

                        if (nameField) nameField.value = 'زبون تجريبي';
                        if (phoneField) phoneField.value = '0555123456';
                        if (addressField) addressField.value = 'عنوان تجريبي';

                        console.log('📝 تم ملء البيانات التجريبية');

                        // محاولة النقر على زر الحفظ
                        setTimeout(() => {
                            console.log('🖱️ محاولة النقر على زر الحفظ...');
                            submitBtn.click();
                        }, 1000);
                    }
                }
            }, 500);
        }
    }, 2000);

    console.log('✅ تم تحميل التطبيق بنجاح');
});

// ===============================
// إدارة بطاقات الغاز
// ===============================

function setupGasCardsForm() {
    console.log('🔧 إعداد نموذج بطاقات الغاز...');

    // إعداد زر إضافة بطاقة جديدة
    const addCardBtn = document.getElementById('add-card-btn');
    console.log('🔍 البحث عن زر إضافة بطاقة...', addCardBtn);

    if (addCardBtn) {
        console.log('✅ تم العثور على زر إضافة بطاقة');
        addCardBtn.addEventListener('click', (e) => {
            e.preventDefault();
            console.log('🖱️ تم النقر على زر إضافة بطاقة');
            openGasCardModal();
        });
    } else {
        console.error('❌ لم يتم العثور على زر إضافة بطاقة');
    }

    // إعداد زر طباعة التقرير
    const printCardsBtn = document.getElementById('print-cards-btn');
    if (printCardsBtn) {
        printCardsBtn.addEventListener('click', (e) => {
            e.preventDefault();
            console.log('🖱️ تم النقر على زر طباعة بطاقات');
            printGasCardsReport();
        });
    }

    // إعداد البحث
    const searchCards = document.getElementById('search-cards');
    if (searchCards) {
        searchCards.addEventListener('input', searchGasCards);
    }
}

function openGasCardModal(cardId = null) {
    console.log('🔓 فتح نافذة بطاقة الغاز...', cardId);

    // إنشاء نافذة منبثقة بسيطة للآن
    const cardData = {
        cardNumber: prompt('رقم البطاقة:') || '',
        customerName: prompt('اسم الزبون:') || '',
        issueDate: new Date().toISOString().split('T')[0],
        expiryDate: prompt('تاريخ الانتهاء (YYYY-MM-DD):') || ''
    };

    if (cardData.cardNumber && cardData.customerName) {
        // تهيئة بيانات البطاقات إذا لم تكن موجودة
        if (!appData.gasCards) {
            appData.gasCards = [];
        }

        const newCard = {
            id: generateId(),
            ...cardData,
            createdAt: new Date().toISOString()
        };

        appData.gasCards.push(newCard);
        saveData();
        updateGasCardsTable();
        showToast('تم إضافة البطاقة بنجاح');

        console.log('✅ تم إضافة البطاقة:', newCard);
    } else {
        console.log('❌ تم إلغاء إضافة البطاقة');
    }
}

function updateGasCardsTable() {
    console.log('🔄 تحديث جدول بطاقات الغاز...');

    const tableBody = document.querySelector('#gas-cards-table tbody');
    if (!tableBody) {
        console.error('❌ لم يتم العثور على جدول بطاقات الغاز');
        return;
    }

    tableBody.innerHTML = '';

    const gasCards = appData.gasCards || [];
    console.log('📊 عدد البطاقات:', gasCards.length);

    if (gasCards.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td colspan="5" style="text-align: center; color: #6b7280; padding: 2rem;">
                <i class="fas fa-id-card" style="font-size: 2rem; margin-bottom: 1rem; display: block;"></i>
                لا توجد بطاقات غاز
            </td>
        `;
        tableBody.appendChild(row);
        return;
    }

    gasCards.forEach(card => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${card.cardNumber}</td>
            <td>${card.customerName}</td>
            <td>${formatDate(card.issueDate)}</td>
            <td>${formatDate(card.expiryDate)}</td>
            <td>
                <button class="btn btn-sm" onclick="editGasCard('${card.id}')" title="تحرير">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-sm success" onclick="printGasCard('${card.id}')" title="طباعة">
                    <i class="fas fa-print"></i>
                </button>
                <button class="btn btn-sm danger" onclick="deleteGasCard('${card.id}')" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tableBody.appendChild(row);
    });
}

function searchGasCards() {
    const searchTerm = document.getElementById('search-cards').value.toLowerCase();
    const rows = document.querySelectorAll('#gas-cards-table tbody tr');

    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(searchTerm) ? '' : 'none';
    });
}

function printGasCardsReport() {
    showToast('ميزة طباعة التقرير قيد التطوير');
}

function editGasCard(cardId) {
    showToast('ميزة تحرير البطاقة قيد التطوير');
}

function printGasCard(cardId) {
    showToast('ميزة طباعة البطاقة قيد التطوير');
}

function deleteGasCard(cardId) {
    if (confirm('هل أنت متأكد من حذف هذه البطاقة؟')) {
        appData.gasCards = appData.gasCards.filter(card => card.id !== cardId);
        saveData();
        updateGasCardsTable();
        updateDashboard();
        showToast('تم حذف البطاقة بنجاح');
    }
}

// ===============================
// إدارة المواعيد
// ===============================

function setupAppointmentsForm() {
    console.log('🔧 إعداد نموذج المواعيد...');

    // إعداد زر إضافة موعد جديد
    const addAppointmentBtn = document.getElementById('add-appointment-btn');
    console.log('🔍 البحث عن زر إضافة موعد...', addAppointmentBtn);

    if (addAppointmentBtn) {
        console.log('✅ تم العثور على زر إضافة موعد');
        addAppointmentBtn.addEventListener('click', (e) => {
            e.preventDefault();
            console.log('🖱️ تم النقر على زر إضافة موعد');
            openAppointmentModal();
        });
    } else {
        console.error('❌ لم يتم العثور على زر إضافة موعد');
    }

    // إعداد زر طباعة التقرير
    const printAppointmentsBtn = document.getElementById('print-appointments-btn');
    if (printAppointmentsBtn) {
        printAppointmentsBtn.addEventListener('click', (e) => {
            e.preventDefault();
            console.log('🖱️ تم النقر على زر طباعة مواعيد');
            printAppointmentsReport();
        });
    }
}

function openAppointmentModal(appointmentId = null) {
    console.log('🔓 فتح نافذة الموعد...', appointmentId);

    // إنشاء نافذة منبثقة بسيطة للآن
    const appointmentData = {
        customerName: prompt('اسم الزبون:') || '',
        appointmentDate: prompt('تاريخ الموعد (YYYY-MM-DD):') || '',
        appointmentTime: prompt('وقت الموعد (HH:MM):') || '',
        serviceType: prompt('نوع الخدمة:') || '',
        notes: prompt('ملاحظات:') || ''
    };

    if (appointmentData.customerName && appointmentData.appointmentDate) {
        // تهيئة بيانات المواعيد إذا لم تكن موجودة
        if (!appData.appointments) {
            appData.appointments = [];
        }

        const newAppointment = {
            id: generateId(),
            ...appointmentData,
            status: 'scheduled',
            createdAt: new Date().toISOString()
        };

        appData.appointments.push(newAppointment);
        saveData();
        updateAppointmentsTable();
        updateDashboard();
        showToast('تم إضافة الموعد بنجاح');

        console.log('✅ تم إضافة الموعد:', newAppointment);
    } else {
        console.log('❌ تم إلغاء إضافة الموعد');
    }
}

function updateAppointmentsTable() {
    console.log('🔄 تحديث جدول المواعيد...');

    const tableBody = document.querySelector('#appointments-table tbody');
    if (!tableBody) {
        console.error('❌ لم يتم العثور على جدول المواعيد');
        return;
    }

    tableBody.innerHTML = '';

    const appointments = appData.appointments || [];
    console.log('📊 عدد المواعيد:', appointments.length);

    if (appointments.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td colspan="6" style="text-align: center; color: #6b7280; padding: 2rem;">
                <i class="fas fa-calendar" style="font-size: 2rem; margin-bottom: 1rem; display: block;"></i>
                لا توجد مواعيد
            </td>
        `;
        tableBody.appendChild(row);
        return;
    }

    appointments.forEach(appointment => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${appointment.customerName}</td>
            <td>${formatDate(appointment.appointmentDate)}</td>
            <td>${appointment.appointmentTime}</td>
            <td>${appointment.serviceType}</td>
            <td><span class="status ${appointment.status}">${getStatusText(appointment.status)}</span></td>
            <td>
                <button class="btn btn-sm" onclick="editAppointment('${appointment.id}')" title="تحرير">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-sm success" onclick="completeAppointment('${appointment.id}')" title="إكمال">
                    <i class="fas fa-check"></i>
                </button>
                <button class="btn btn-sm danger" onclick="deleteAppointment('${appointment.id}')" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tableBody.appendChild(row);
    });
}

function getStatusText(status) {
    const statuses = {
        'scheduled': 'مجدول',
        'completed': 'مكتمل',
        'cancelled': 'ملغي',
        'in-progress': 'قيد التنفيذ'
    };
    return statuses[status] || status;
}

function printAppointmentsReport() {
    showToast('ميزة طباعة التقرير قيد التطوير');
}

function editAppointment(appointmentId) {
    showToast('ميزة تحرير الموعد قيد التطوير');
}

function completeAppointment(appointmentId) {
    const appointment = appData.appointments.find(a => a.id === appointmentId);
    if (appointment) {
        appointment.status = 'completed';
        saveData();
        updateAppointmentsTable();
        updateDashboard();
        showToast('تم إكمال الموعد بنجاح');
    }
}

function deleteAppointment(appointmentId) {
    if (confirm('هل أنت متأكد من حذف هذا الموعد؟')) {
        appData.appointments = appData.appointments.filter(appointment => appointment.id !== appointmentId);
        saveData();
        updateAppointmentsTable();
        updateDashboard();
        showToast('تم حذف الموعد بنجاح');
    }
}

// ===============================
// نظام إدارة الشهادات
// ===============================

// إعداد نظام الشهادات
function setupCertificatesSystem() {
    // إعداد التبويبات
    setupCertificatesTabs();

    // إعداد النماذج
    setupCertificateForm();

    // إعداد الأحداث
    setupCertificatesEvents();

    // تحميل البيانات
    loadCertificatesData();
}

// إعداد تبويبات الشهادات
function setupCertificatesTabs() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const targetTab = button.getAttribute('data-tab');

            // إزالة الفئة النشطة من جميع الأزرار والمحتويات
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));

            // إضافة الفئة النشطة للزر والمحتوى المحدد
            button.classList.add('active');
            document.getElementById(targetTab + '-tab').classList.add('active');

            // تحديث البيانات حسب التبويب
            updateCertificatesTab(targetTab);
        });
    });

    // إعداد تبويبات التذكيرات الفرعية
    const reminderTabButtons = document.querySelectorAll('.reminder-tab-btn');
    const reminderTabContents = document.querySelectorAll('.reminder-tab-content');

    reminderTabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const targetTab = button.getAttribute('data-tab');

            reminderTabButtons.forEach(btn => btn.classList.remove('active'));
            reminderTabContents.forEach(content => content.classList.remove('active'));

            button.classList.add('active');
            document.getElementById(targetTab).classList.add('active');

            updateRemindersTab(targetTab);
        });
    });
}

// إعداد نموذج الشهادات
function setupCertificateForm() {
    const form = document.getElementById('certificate-form');
    if (form) {
        form.addEventListener('submit', saveCertificate);
    }

    const certificateType = document.getElementById('certificate-type');
    if (certificateType) {
        certificateType.addEventListener('change', toggleCertificateDetails);
    }

    // تحميل قائمة الزبائن
    loadCustomersForCertificate();
}

// إعداد أحداث الشهادات
function setupCertificatesEvents() {
    // زر إضافة شهادة جديدة
    const addBtn = document.getElementById('add-certificate-btn');
    if (addBtn) {
        addBtn.addEventListener('click', openCertificateModal);
    }

    // البحث في الشهادات
    const searchInput = document.getElementById('search-certificates');
    if (searchInput) {
        searchInput.addEventListener('input', searchCertificates);
    }

    // أزرار الفلاتر
    setupCertificateFilters();
}

// إعداد فلاتر الشهادات
function setupCertificateFilters() {
    const filters = [
        'installation-status-filter',
        'monitoring-status-filter',
        'warranty-status-filter'
    ];

    filters.forEach(filterId => {
        const filter = document.getElementById(filterId);
        if (filter) {
            filter.addEventListener('change', applyCertificateFilters);
        }
    });

    // فلاتر التاريخ
    const dateFilters = [
        'installation-date-from', 'installation-date-to',
        'monitoring-date-from', 'monitoring-date-to',
        'warranty-date-from', 'warranty-date-to'
    ];

    dateFilters.forEach(filterId => {
        const filter = document.getElementById(filterId);
        if (filter) {
            filter.addEventListener('change', applyCertificateFilters);
        }
    });
}

// تحميل بيانات الشهادات
function loadCertificatesData() {
    // تهيئة بيانات الشهادات إذا لم تكن موجودة
    if (!appData.certificates) {
        appData.certificates = [];
    }

    // تحويل البيانات القديمة إذا كانت بالتنسيق الجديد
    if (appData.certificates && typeof appData.certificates === 'object' && !Array.isArray(appData.certificates)) {
        const oldCertificates = appData.certificates;
        appData.certificates = [];

        // دمج جميع الأنواع في مصفوفة واحدة
        if (oldCertificates.installation) {
            appData.certificates.push(...oldCertificates.installation);
        }
        if (oldCertificates.monitoring) {
            appData.certificates.push(...oldCertificates.monitoring);
        }
        if (oldCertificates.warranty) {
            appData.certificates.push(...oldCertificates.warranty);
        }

        saveData(); // حفظ التحويل
    }

    // تحديث الإحصائيات
    updateCertificatesStats();

    // تحديث الجداول
    updateAllCertificatesTables();

    // تحديث التذكيرات
    updateCertificateReminders();
}

// تحديث إحصائيات الشهادات
function updateCertificatesStats() {
    const certificates = appData.certificates || [];

    // عدد الشهادات حسب النوع
    const installationCount = certificates.filter(cert => cert.type === 'installation').length;
    const monitoringCount = certificates.filter(cert => cert.type === 'monitoring').length;
    const warrantyCount = certificates.filter(cert => cert.type === 'warranty').length;

    const installationEl = document.getElementById('installation-certificates-count');
    const monitoringEl = document.getElementById('monitoring-certificates-count');
    const warrantyEl = document.getElementById('warranty-certificates-count');

    if (installationEl) installationEl.textContent = installationCount;
    if (monitoringEl) monitoringEl.textContent = monitoringCount;
    if (warrantyEl) warrantyEl.textContent = warrantyCount;

    // الشهادات المنتهية الصلاحية
    const today = new Date();
    const expiredCount = certificates.filter(cert => {
        const expiryDate = new Date(cert.expiryDate || cert.nextMonitoring);
        return expiryDate < today;
    }).length;

    const expiredEl = document.getElementById('expiring-certificates-count');
    if (expiredEl) expiredEl.textContent = expiredCount;
}

// تحديث جميع جداول الشهادات
function updateAllCertificatesTables() {
    updateInstallationCertificatesTable();
    updateMonitoringCertificatesTable();
    updateWarrantyCertificatesTable();
}

// تحديث جدول شهادات التركيب
function updateInstallationCertificatesTable() {
    const tableBody = document.querySelector('#installation-certificates-table tbody');
    const emptyState = document.getElementById('installation-empty-state');

    if (!tableBody) return;

    const certificates = appData.certificates.installation || [];
    tableBody.innerHTML = '';

    if (certificates.length === 0) {
        if (emptyState) emptyState.style.display = 'block';
        return;
    }

    if (emptyState) emptyState.style.display = 'none';

    certificates.forEach(cert => {
        const customer = appData.customers.find(c => c.id === cert.customerId);
        const vehicle = appData.vehicles.find(v => v.customerId === cert.customerId);
        const expiryDate = new Date(cert.expiryDate);
        const today = new Date();
        const isExpired = expiryDate < today;

        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${cert.certificateNumber}</td>
            <td>${customer ? customer.name : 'غير محدد'}</td>
            <td>${vehicle ? vehicle.plateNumber : 'غير محدد'}</td>
            <td>${vehicle ? vehicle.brand + ' ' + vehicle.model : 'غير محدد'}</td>
            <td>${formatDate(cert.issueDate)}</td>
            <td>${formatDate(cert.expiryDate)}</td>
            <td><span class="certificate-status ${isExpired ? 'expired' : 'active'}">${isExpired ? 'منتهية' : 'نشطة'}</span></td>
            <td>
                <button class="btn btn-sm" onclick="editCertificate('installation', '${cert.id}')" title="تحرير">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-sm success" onclick="printCertificate('installation', '${cert.id}')" title="طباعة">
                    <i class="fas fa-print"></i>
                </button>
                <button class="btn btn-sm danger" onclick="deleteCertificate('installation', '${cert.id}')" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tableBody.appendChild(row);
    });
}

// تحديث جدول شهادات المراقبة
function updateMonitoringCertificatesTable() {
    const tableBody = document.querySelector('#monitoring-certificates-table tbody');
    const emptyState = document.getElementById('monitoring-empty-state');

    if (!tableBody) return;

    const certificates = appData.certificates.monitoring || [];
    tableBody.innerHTML = '';

    if (certificates.length === 0) {
        if (emptyState) emptyState.style.display = 'block';
        return;
    }

    if (emptyState) emptyState.style.display = 'none';

    certificates.forEach(cert => {
        const customer = appData.customers.find(c => c.id === cert.customerId);
        const vehicle = appData.vehicles.find(v => v.customerId === cert.customerId);
        const nextMonitoring = new Date(cert.nextMonitoring);
        const today = new Date();
        const isDue = nextMonitoring < today;

        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${cert.certificateNumber}</td>
            <td>${customer ? customer.name : 'غير محدد'}</td>
            <td>${vehicle ? vehicle.plateNumber : 'غير محدد'}</td>
            <td>${formatDate(cert.monitoringDate)}</td>
            <td>${formatDate(cert.nextMonitoring)}</td>
            <td><span class="certificate-status ${cert.result}">${getMonitoringResultText(cert.result)}</span></td>
            <td><span class="certificate-status ${isDue ? 'overdue' : 'valid'}">${isDue ? 'متأخرة' : 'صالحة'}</span></td>
            <td>
                <button class="btn btn-sm" onclick="editCertificate('monitoring', '${cert.id}')" title="تحرير">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-sm success" onclick="printCertificate('monitoring', '${cert.id}')" title="طباعة">
                    <i class="fas fa-print"></i>
                </button>
                <button class="btn btn-sm info" onclick="scheduleNextMonitoring('${cert.id}')" title="جدولة المراقبة القادمة">
                    <i class="fas fa-calendar-plus"></i>
                </button>
                <button class="btn btn-sm danger" onclick="deleteCertificate('monitoring', '${cert.id}')" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tableBody.appendChild(row);
    });
}

// تحديث جدول شهادات الضمان
function updateWarrantyCertificatesTable() {
    const tableBody = document.querySelector('#warranty-certificates-table tbody');
    const emptyState = document.getElementById('warranty-empty-state');

    if (!tableBody) return;

    const certificates = appData.certificates.warranty || [];
    tableBody.innerHTML = '';

    if (certificates.length === 0) {
        if (emptyState) emptyState.style.display = 'block';
        return;
    }

    if (emptyState) emptyState.style.display = 'none';

    certificates.forEach(cert => {
        const customer = appData.customers.find(c => c.id === cert.customerId);
        const vehicle = appData.vehicles.find(v => v.customerId === cert.customerId);
        const endDate = new Date(cert.endDate);
        const today = new Date();
        const isExpired = endDate < today;

        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${cert.warrantyNumber}</td>
            <td>${customer ? customer.name : 'غير محدد'}</td>
            <td>${vehicle ? vehicle.plateNumber : 'غير محدد'}</td>
            <td>${getWarrantyTypeText(cert.warrantyType)}</td>
            <td>${formatDate(cert.startDate)}</td>
            <td>${formatDate(cert.endDate)}</td>
            <td><span class="certificate-status ${isExpired ? 'expired' : 'active'}">${isExpired ? 'منتهية' : 'نشطة'}</span></td>
            <td>
                <button class="btn btn-sm" onclick="editCertificate('warranty', '${cert.id}')" title="تحرير">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-sm success" onclick="printCertificate('warranty', '${cert.id}')" title="طباعة">
                    <i class="fas fa-print"></i>
                </button>
                <button class="btn btn-sm danger" onclick="deleteCertificate('warranty', '${cert.id}')" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tableBody.appendChild(row);
    });
}

// وظائف مساعدة
function getMonitoringResultText(result) {
    const results = {
        'passed': 'مقبول',
        'failed': 'مرفوض',
        'conditional': 'مشروط'
    };
    return results[result] || result;
}

function getWarrantyTypeText(type) {
    const types = {
        'parts': 'ضمان القطع',
        'labor': 'ضمان العمالة',
        'full': 'ضمان شامل'
    };
    return types[type] || type;
}

// فتح نموذج إضافة شهادة
function openCertificateModal(type = null, certificateId = null) {
    const modal = document.getElementById('certificate-modal');
    const title = document.getElementById('certificate-modal-title');

    if (certificateId) {
        title.textContent = 'تحرير الشهادة';
        loadCertificateData(type, certificateId);
    } else {
        title.textContent = 'إضافة شهادة جديدة';
        resetCertificateForm();
    }

    modal.style.display = 'block';
}

// إغلاق نموذج الشهادة
function closeCertificateModal() {
    const modal = document.getElementById('certificate-modal');
    modal.style.display = 'none';
    resetCertificateForm();
}

// إعادة تعيين نموذج الشهادة
function resetCertificateForm() {
    const form = document.getElementById('certificate-form');
    if (form) {
        form.reset();
        document.getElementById('certificate-id').value = '';

        // إخفاء جميع أقسام التفاصيل
        document.getElementById('installation-details').style.display = 'none';
        document.getElementById('monitoring-details').style.display = 'none';
        document.getElementById('warranty-details').style.display = 'none';

        // تعيين تاريخ اليوم كافتراضي
        document.getElementById('certificate-issue-date').value = new Date().toISOString().split('T')[0];
    }
}

// تبديل تفاصيل الشهادة حسب النوع
function toggleCertificateDetails() {
    const certificateType = document.getElementById('certificate-type').value;

    // إخفاء جميع الأقسام
    document.getElementById('installation-details').style.display = 'none';
    document.getElementById('monitoring-details').style.display = 'none';
    document.getElementById('warranty-details').style.display = 'none';

    // إظهار القسم المناسب
    if (certificateType === 'installation') {
        document.getElementById('installation-details').style.display = 'block';
    } else if (certificateType === 'monitoring') {
        document.getElementById('monitoring-details').style.display = 'block';
    } else if (certificateType === 'warranty') {
        document.getElementById('warranty-details').style.display = 'block';
    }
}

// تحميل قائمة الزبائن للشهادة
function loadCustomersForCertificate() {
    const customerSelect = document.getElementById('certificate-customer');
    if (!customerSelect) return;

    customerSelect.innerHTML = '<option value="">اختر الزبون</option>';

    appData.customers.forEach(customer => {
        const option = document.createElement('option');
        option.value = customer.id;
        option.textContent = `${customer.name} - ${customer.phone}`;
        customerSelect.appendChild(option);
    });
}

// حفظ الشهادة
function saveCertificate(e) {
    e.preventDefault();

    const certificateId = document.getElementById('certificate-id').value;
    const certificateType = document.getElementById('certificate-type').value;

    if (!certificateType) {
        showToast('يرجى اختيار نوع الشهادة', false);
        return;
    }

    const certificateData = {
        id: certificateId || generateId(),
        type: certificateType,
        customerId: document.getElementById('certificate-customer').value,
        issueDate: document.getElementById('certificate-issue-date').value,
        createdAt: certificateId ? undefined : new Date().toISOString()
    };

    // إضافة البيانات حسب نوع الشهادة
    if (certificateType === 'installation') {
        Object.assign(certificateData, {
            certificateNumber: document.getElementById('certificate-number').value,
            location: document.getElementById('installation-location').value,
            technician: document.getElementById('installation-technician').value,
            validity: parseInt(document.getElementById('installation-validity').value) || 12,
            notes: document.getElementById('installation-notes').value,
            expiryDate: calculateExpiryDate(certificateData.issueDate, parseInt(document.getElementById('installation-validity').value) || 12)
        });
    } else if (certificateType === 'monitoring') {
        Object.assign(certificateData, {
            certificateNumber: document.getElementById('certificate-number').value,
            monitoringDate: certificateData.issueDate,
            result: document.getElementById('monitoring-result').value,
            nextMonitoring: document.getElementById('next-monitoring').value,
            inspector: document.getElementById('monitoring-inspector').value,
            defects: document.getElementById('monitoring-defects').value
        });
    } else if (certificateType === 'warranty') {
        const duration = parseInt(document.getElementById('warranty-duration').value) || 12;
        Object.assign(certificateData, {
            warrantyNumber: document.getElementById('certificate-number').value,
            warrantyType: document.getElementById('warranty-type').value,
            duration: duration,
            startDate: certificateData.issueDate,
            endDate: calculateExpiryDate(certificateData.issueDate, duration),
            conditions: document.getElementById('warranty-conditions').value
        });
    }

    // التحقق من البيانات المطلوبة
    if (!certificateData.customerId || !certificateData.certificateNumber) {
        showToast('يرجى ملء جميع الحقول المطلوبة', false);
        return;
    }

    // حفظ الشهادة
    if (certificateId) {
        // تحديث شهادة موجودة
        const index = appData.certificates[certificateType].findIndex(c => c.id === certificateId);
        if (index !== -1) {
            appData.certificates[certificateType][index] = certificateData;
        }
    } else {
        // إضافة شهادة جديدة
        appData.certificates[certificateType].push(certificateData);
    }

    // حفظ البيانات وتحديث الواجهة
    saveData();
    updateCertificatesStats();
    updateAllCertificatesTables();
    updateCertificateReminders();

    // إضافة إلى جدول الإرسال إذا كان تركيب أو مراقبة
    if (certificateType === 'installation' || certificateType === 'monitoring') {
        addToTransmissionTable(certificateData);
    }

    closeCertificateModal();
    showToast(certificateId ? 'تم تحديث الشهادة بنجاح' : 'تم إضافة الشهادة بنجاح');
}

// حساب تاريخ الانتهاء
function calculateExpiryDate(startDate, months) {
    const date = new Date(startDate);
    date.setMonth(date.getMonth() + months);
    return date.toISOString().split('T')[0];
}

// إضافة إلى جدول الإرسال
function addToTransmissionTable(certificateData) {
    if (!appData.transmissionTable) {
        appData.transmissionTable = [];
    }

    const customer = appData.customers.find(c => c.id === certificateData.customerId);
    const vehicle = appData.vehicles.find(v => v.customerId === certificateData.customerId);

    if (customer && vehicle) {
        const transmissionEntry = {
            id: generateId(),
            customerId: certificateData.customerId,
            vehicleId: vehicle.id,
            operationType: certificateData.type === 'installation' ? 'تركيب' : 'مراقبة',
            operationDate: certificateData.issueDate,
            certificateNumber: certificateData.certificateNumber || certificateData.warrantyNumber,
            notes: certificateData.notes || certificateData.defects || '',
            createdAt: new Date().toISOString()
        };

        appData.transmissionTable.push(transmissionEntry);
        updateTransmissionTable();
    }
}

// تحرير شهادة
function editCertificate(type, certificateId) {
    openCertificateModal(type, certificateId);
}

// تحميل بيانات الشهادة للتحرير
function loadCertificateData(type, certificateId) {
    const certificate = appData.certificates[type].find(c => c.id === certificateId);
    if (!certificate) return;

    // ملء البيانات الأساسية
    document.getElementById('certificate-id').value = certificate.id;
    document.getElementById('certificate-type').value = certificate.type;
    document.getElementById('certificate-customer').value = certificate.customerId;
    document.getElementById('certificate-issue-date').value = certificate.issueDate;
    document.getElementById('certificate-number').value = certificate.certificateNumber || certificate.warrantyNumber;

    // إظهار التفاصيل المناسبة
    toggleCertificateDetails();

    // ملء التفاصيل حسب النوع
    if (type === 'installation') {
        document.getElementById('installation-location').value = certificate.location || '';
        document.getElementById('installation-technician').value = certificate.technician || '';
        document.getElementById('installation-validity').value = certificate.validity || 12;
        document.getElementById('installation-notes').value = certificate.notes || '';
    } else if (type === 'monitoring') {
        document.getElementById('monitoring-result').value = certificate.result || '';
        document.getElementById('next-monitoring').value = certificate.nextMonitoring || '';
        document.getElementById('monitoring-inspector').value = certificate.inspector || '';
        document.getElementById('monitoring-defects').value = certificate.defects || '';
    } else if (type === 'warranty') {
        document.getElementById('warranty-type').value = certificate.warrantyType || '';
        document.getElementById('warranty-duration').value = certificate.duration || 12;
        document.getElementById('warranty-conditions').value = certificate.conditions || '';
    }
}

// حذف شهادة
function deleteCertificate(type, certificateId) {
    const certificate = appData.certificates[type].find(c => c.id === certificateId);
    if (!certificate) return;

    const customer = appData.customers.find(c => c.id === certificate.customerId);
    const customerName = customer ? customer.name : 'غير محدد';

    if (confirm(`هل أنت متأكد من حذف شهادة ${getTypeText(type)} للزبون ${customerName}؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        // حذف الشهادة
        appData.certificates[type] = appData.certificates[type].filter(c => c.id !== certificateId);

        // حفظ البيانات وتحديث الواجهة
        saveData();
        updateCertificatesStats();
        updateAllCertificatesTables();
        updateCertificateReminders();

        showToast('تم حذف الشهادة بنجاح');
    }
}

function getTypeText(type) {
    const types = {
        'installation': 'التركيب',
        'monitoring': 'المراقبة',
        'warranty': 'الضمان'
    };
    return types[type] || type;
}

// طباعة شهادة
function printCertificate(type, certificateId) {
    const certificate = appData.certificates[type].find(c => c.id === certificateId);
    if (!certificate) return;

    const customer = appData.customers.find(c => c.id === certificate.customerId);
    const vehicle = appData.vehicles.find(v => v.customerId === certificate.customerId);
    const tank = appData.gasTanks.find(t => t.vehicleId === vehicle?.id);

    // إنشاء نافذة طباعة
    const printWindow = window.open('', '_blank');
    const certificateHtml = generateCertificateHTML(type, certificate, customer, vehicle, tank);

    printWindow.document.write(certificateHtml);
    printWindow.document.close();
    printWindow.print();
}

// إنشاء HTML للشهادة
function generateCertificateHTML(type, certificate, customer, vehicle, tank) {
    const settings = appData.settings || {};
    const companyName = settings.companyName || 'مركز وقود المستقبل';
    const licenseNumber = settings.licenseNumber || '463/2019';
    const wilaya = settings.companyWilaya || 'المدية';

    let certificateTitle = '';
    let certificateContent = '';

    if (type === 'installation') {
        certificateTitle = 'شهادة تركيب نظام غاز البترول المميع';
        certificateContent = `
            <p><strong>رقم الشهادة:</strong> ${certificate.certificateNumber}</p>
            <p><strong>تاريخ التركيب:</strong> ${formatDate(certificate.issueDate)}</p>
            <p><strong>مكان التركيب:</strong> ${certificate.location || 'غير محدد'}</p>
            <p><strong>الفني المسؤول:</strong> ${certificate.technician || 'غير محدد'}</p>
            <p><strong>صالحة حتى:</strong> ${formatDate(certificate.expiryDate)}</p>
            ${certificate.notes ? `<p><strong>ملاحظات:</strong> ${certificate.notes}</p>` : ''}
        `;
    } else if (type === 'monitoring') {
        certificateTitle = 'شهادة مراقبة دورية لنظام غاز البترول المميع';
        certificateContent = `
            <p><strong>رقم الشهادة:</strong> ${certificate.certificateNumber}</p>
            <p><strong>تاريخ المراقبة:</strong> ${formatDate(certificate.monitoringDate)}</p>
            <p><strong>نتيجة المراقبة:</strong> ${getMonitoringResultText(certificate.result)}</p>
            <p><strong>المفتش:</strong> ${certificate.inspector || 'غير محدد'}</p>
            <p><strong>المراقبة القادمة:</strong> ${formatDate(certificate.nextMonitoring)}</p>
            ${certificate.defects ? `<p><strong>العيوب المكتشفة:</strong> ${certificate.defects}</p>` : ''}
        `;
    } else if (type === 'warranty') {
        certificateTitle = 'شهادة ضمان';
        certificateContent = `
            <p><strong>رقم الضمان:</strong> ${certificate.warrantyNumber}</p>
            <p><strong>نوع الضمان:</strong> ${getWarrantyTypeText(certificate.warrantyType)}</p>
            <p><strong>تاريخ البداية:</strong> ${formatDate(certificate.startDate)}</p>
            <p><strong>تاريخ الانتهاء:</strong> ${formatDate(certificate.endDate)}</p>
            <p><strong>مدة الضمان:</strong> ${certificate.duration} شهر</p>
            ${certificate.conditions ? `<p><strong>شروط الضمان:</strong> ${certificate.conditions}</p>` : ''}
        `;
    }

    return `
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>${certificateTitle}</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
                .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 30px; }
                .certificate-title { font-size: 24px; font-weight: bold; color: #2563eb; margin: 20px 0; }
                .company-info { margin-bottom: 20px; }
                .customer-info, .vehicle-info, .certificate-details { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
                .section-title { font-weight: bold; color: #1f2937; border-bottom: 1px solid #e5e7eb; padding-bottom: 5px; margin-bottom: 10px; }
                .footer { margin-top: 50px; text-align: center; border-top: 1px solid #ddd; padding-top: 20px; }
                @media print { body { margin: 0; } }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>الجمهورية الجزائرية الديمقراطية الشعبية</h1>
                <h2>${companyName}</h2>
                <p>رقم الاعتماد: ${licenseNumber} - ${wilaya}</p>
            </div>

            <div class="certificate-title">${certificateTitle}</div>

            <div class="customer-info">
                <div class="section-title">معلومات الزبون</div>
                <p><strong>الاسم:</strong> ${customer?.name || 'غير محدد'}</p>
                <p><strong>رقم الهاتف:</strong> ${customer?.phone || 'غير محدد'}</p>
                <p><strong>العنوان:</strong> ${customer?.address || 'غير محدد'}</p>
            </div>

            <div class="vehicle-info">
                <div class="section-title">معلومات المركبة</div>
                <p><strong>رقم التسجيل:</strong> ${vehicle?.plateNumber || 'غير محدد'}</p>
                <p><strong>الماركة والموديل:</strong> ${vehicle ? `${vehicle.brand} ${vehicle.model}` : 'غير محدد'}</p>
                <p><strong>سنة الصنع:</strong> ${vehicle?.year || 'غير محدد'}</p>
                <p><strong>رقم الهيكل:</strong> ${vehicle?.chassisNumber || 'غير محدد'}</p>
                ${tank ? `
                    <p><strong>نوع الخزان:</strong> ${tank.type || 'غير محدد'}</p>
                    <p><strong>سعة الخزان:</strong> ${tank.capacity || 'غير محدد'} لتر</p>
                    <p><strong>الرقم التسلسلي للخزان:</strong> ${tank.serialNumber || 'غير محدد'}</p>
                ` : ''}
            </div>

            <div class="certificate-details">
                <div class="section-title">تفاصيل الشهادة</div>
                ${certificateContent}
            </div>

            <div class="footer">
                <p>تاريخ الإصدار: ${formatDateWithFrenchMonth(new Date().toISOString().split('T')[0])}</p>
                <p>مكان الإصدار: ${settings.issueLocation || wilaya}</p>
                ${settings.expertName ? `<p>الخبير: ${settings.expertName}</p>` : ''}
                <br>
                <p>ختم وتوقيع المؤسسة</p>
            </div>
        </body>
        </html>
    `;
}

// ===============================
// نظام إدارة الموردين
// ===============================

// إعداد نظام الموردين
function setupSuppliersSystem() {
    // تهيئة بيانات الموردين
    if (!appData.suppliers) {
        appData.suppliers = [];
    }

    // إعداد الأحداث
    setupSuppliersEvents();

    // تحميل البيانات
    loadSuppliersData();
}

// إعداد أحداث الموردين
function setupSuppliersEvents() {
    // زر إضافة مورد جديد
    const addBtn = document.getElementById('add-supplier-btn');
    if (addBtn) {
        addBtn.addEventListener('click', openSupplierModal);
    }

    // البحث في الموردين
    const searchInput = document.getElementById('search-suppliers');
    if (searchInput) {
        searchInput.addEventListener('input', searchSuppliers);
    }

    // نموذج المورد
    const form = document.getElementById('supplier-form');
    if (form) {
        form.addEventListener('submit', saveSupplier);
    }

    // فلاتر الموردين
    setupSupplierFilters();
}

// إعداد فلاتر الموردين
function setupSupplierFilters() {
    const filters = [
        'supplier-status-filter',
        'supplier-category-filter',
        'supplier-location-filter'
    ];

    filters.forEach(filterId => {
        const filter = document.getElementById(filterId);
        if (filter) {
            filter.addEventListener('change', applySupplierFilters);
        }
    });

    const clearBtn = document.getElementById('clear-supplier-filters');
    if (clearBtn) {
        clearBtn.addEventListener('click', clearSupplierFilters);
    }
}

// تحميل بيانات الموردين
function loadSuppliersData() {
    updateSuppliersStats();
    updateSuppliersTable();
}

// تحديث إحصائيات الموردين
function updateSuppliersStats() {
    const suppliers = appData.suppliers || [];

    // إجمالي الموردين
    document.getElementById('total-suppliers-count').textContent = suppliers.length;

    // المنتجات المتاحة (محاكاة)
    const productsCount = suppliers.reduce((sum, supplier) => sum + (supplier.productsCount || 0), 0);
    document.getElementById('supplier-products-count').textContent = productsCount;

    // أوامر الشراء (محاكاة)
    document.getElementById('supplier-orders-count').textContent = '0';

    // أوامر معلقة (محاكاة)
    document.getElementById('pending-orders-count').textContent = '0';
}

// تحديث جدول الموردين
function updateSuppliersTable() {
    const tableBody = document.querySelector('#suppliers-table tbody');
    const emptyState = document.getElementById('suppliers-empty-state');

    if (!tableBody) return;

    const suppliers = appData.suppliers || [];
    tableBody.innerHTML = '';

    if (suppliers.length === 0) {
        if (emptyState) emptyState.style.display = 'block';
        return;
    }

    if (emptyState) emptyState.style.display = 'none';

    suppliers.forEach(supplier => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${supplier.name}</td>
            <td>${supplier.company || '-'}</td>
            <td>${supplier.phone}</td>
            <td>${supplier.email || '-'}</td>
            <td><span class="supplier-category ${supplier.category}">${getCategoryText(supplier.category)}</span></td>
            <td>${getLocationText(supplier.location)}</td>
            <td><span class="supplier-status ${supplier.status}">${getStatusText(supplier.status)}</span></td>
            <td>${supplier.lastOrder ? formatDate(supplier.lastOrder) : 'لا يوجد'}</td>
            <td>
                <button class="btn btn-sm" onclick="editSupplier('${supplier.id}')" title="تحرير">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-sm info" onclick="viewSupplierDetails('${supplier.id}')" title="التفاصيل">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn btn-sm success" onclick="createPurchaseOrder('${supplier.id}')" title="طلب شراء">
                    <i class="fas fa-shopping-cart"></i>
                </button>
                <button class="btn btn-sm danger" onclick="deleteSupplier('${supplier.id}')" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tableBody.appendChild(row);
    });
}

// وظائف مساعدة
function getCategoryText(category) {
    const categories = {
        'gas-equipment': 'معدات الغاز',
        'spare-parts': 'قطع الغيار',
        'tools': 'الأدوات',
        'services': 'الخدمات'
    };
    return categories[category] || category;
}

function getLocationText(location) {
    const locations = {
        'local': 'محلي',
        'national': 'وطني',
        'international': 'دولي'
    };
    return locations[location] || location;
}

function getStatusText(status) {
    const statuses = {
        'active': 'نشط',
        'inactive': 'غير نشط',
        'blocked': 'محظور'
    };
    return statuses[status] || status;
}

// فتح نموذج المورد
function openSupplierModal(supplierId = null) {
    const modal = document.getElementById('supplier-modal');
    const title = document.getElementById('supplier-modal-title');

    if (supplierId) {
        title.textContent = 'تحرير المورد';
        loadSupplierData(supplierId);
    } else {
        title.textContent = 'إضافة مورد جديد';
        resetSupplierForm();
    }

    modal.style.display = 'block';
}

// إغلاق نموذج المورد
function closeSupplierModal() {
    const modal = document.getElementById('supplier-modal');
    modal.style.display = 'none';
    resetSupplierForm();
}

// إعادة تعيين نموذج المورد
function resetSupplierForm() {
    const form = document.getElementById('supplier-form');
    if (form) {
        form.reset();
        document.getElementById('supplier-id').value = '';
        document.getElementById('supplier-status').value = 'active';
    }
}

// تحميل بيانات المورد للتحرير
function loadSupplierData(supplierId) {
    const supplier = appData.suppliers.find(s => s.id === supplierId);
    if (!supplier) return;

    // ملء البيانات الأساسية
    document.getElementById('supplier-id').value = supplier.id;
    document.getElementById('supplier-name').value = supplier.name;
    document.getElementById('supplier-company').value = supplier.company || '';
    document.getElementById('supplier-phone').value = supplier.phone;
    document.getElementById('supplier-email').value = supplier.email || '';
    document.getElementById('supplier-category').value = supplier.category;
    document.getElementById('supplier-location').value = supplier.location;

    // معلومات الاتصال
    document.getElementById('supplier-address').value = supplier.address || '';
    document.getElementById('supplier-website').value = supplier.website || '';
    document.getElementById('supplier-contact-person').value = supplier.contactPerson || '';
    document.getElementById('supplier-contact-phone').value = supplier.contactPhone || '';

    // المعلومات التجارية
    document.getElementById('supplier-tax-number').value = supplier.taxNumber || '';
    document.getElementById('supplier-payment-terms').value = supplier.paymentTerms || '';
    document.getElementById('supplier-credit-limit').value = supplier.creditLimit || '';
    document.getElementById('supplier-discount').value = supplier.discount || '';

    // ملاحظات وحالة
    document.getElementById('supplier-status').value = supplier.status;
    document.getElementById('supplier-rating').value = supplier.rating || '';
    document.getElementById('supplier-notes').value = supplier.notes || '';
}

// حفظ المورد
function saveSupplier(e) {
    e.preventDefault();

    const supplierId = document.getElementById('supplier-id').value;

    const supplierData = {
        id: supplierId || generateId(),
        name: document.getElementById('supplier-name').value,
        company: document.getElementById('supplier-company').value,
        phone: document.getElementById('supplier-phone').value,
        email: document.getElementById('supplier-email').value,
        category: document.getElementById('supplier-category').value,
        location: document.getElementById('supplier-location').value,
        address: document.getElementById('supplier-address').value,
        website: document.getElementById('supplier-website').value,
        contactPerson: document.getElementById('supplier-contact-person').value,
        contactPhone: document.getElementById('supplier-contact-phone').value,
        taxNumber: document.getElementById('supplier-tax-number').value,
        paymentTerms: document.getElementById('supplier-payment-terms').value,
        creditLimit: parseFloat(document.getElementById('supplier-credit-limit').value) || 0,
        discount: parseFloat(document.getElementById('supplier-discount').value) || 0,
        status: document.getElementById('supplier-status').value,
        rating: document.getElementById('supplier-rating').value,
        notes: document.getElementById('supplier-notes').value,
        createdAt: supplierId ? undefined : new Date().toISOString(),
        updatedAt: new Date().toISOString()
    };

    // التحقق من البيانات المطلوبة
    if (!supplierData.name || !supplierData.phone || !supplierData.category || !supplierData.location) {
        showToast('يرجى ملء جميع الحقول المطلوبة', false);
        return;
    }

    // حفظ المورد
    if (supplierId) {
        // تحديث مورد موجود
        const index = appData.suppliers.findIndex(s => s.id === supplierId);
        if (index !== -1) {
            appData.suppliers[index] = supplierData;
        }
    } else {
        // إضافة مورد جديد
        appData.suppliers.push(supplierData);
    }

    // حفظ البيانات وتحديث الواجهة
    saveData();
    updateSuppliersStats();
    updateSuppliersTable();

    closeSupplierModal();
    showToast(supplierId ? 'تم تحديث المورد بنجاح' : 'تم إضافة المورد بنجاح');
}

// تحرير مورد
function editSupplier(supplierId) {
    openSupplierModal(supplierId);
}

// حذف مورد
function deleteSupplier(supplierId) {
    const supplier = appData.suppliers.find(s => s.id === supplierId);
    if (!supplier) return;

    if (confirm(`هل أنت متأكد من حذف المورد "${supplier.name}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        // حذف المورد
        appData.suppliers = appData.suppliers.filter(s => s.id !== supplierId);

        // حفظ البيانات وتحديث الواجهة
        saveData();
        updateSuppliersStats();
        updateSuppliersTable();

        showToast('تم حذف المورد بنجاح');
    }
}

// البحث في الموردين
function searchSuppliers() {
    const searchTerm = document.getElementById('search-suppliers').value.toLowerCase();
    const rows = document.querySelectorAll('#suppliers-table tbody tr');

    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(searchTerm) ? '' : 'none';
    });
}

// تطبيق فلاتر الموردين
function applySupplierFilters() {
    const statusFilter = document.getElementById('supplier-status-filter').value;
    const categoryFilter = document.getElementById('supplier-category-filter').value;
    const locationFilter = document.getElementById('supplier-location-filter').value;

    const rows = document.querySelectorAll('#suppliers-table tbody tr');

    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        const category = cells[4]?.querySelector('.supplier-category')?.classList[1] || '';
        const status = cells[6]?.querySelector('.supplier-status')?.classList[1] || '';
        const location = cells[5]?.textContent.toLowerCase();

        let show = true;

        if (statusFilter && status !== statusFilter) show = false;
        if (categoryFilter && category !== categoryFilter) show = false;
        if (locationFilter && !location.includes(getLocationText(locationFilter).toLowerCase())) show = false;

        row.style.display = show ? '' : 'none';
    });
}

// مسح فلاتر الموردين
function clearSupplierFilters() {
    document.getElementById('supplier-status-filter').value = '';
    document.getElementById('supplier-category-filter').value = '';
    document.getElementById('supplier-location-filter').value = '';

    const rows = document.querySelectorAll('#suppliers-table tbody tr');
    rows.forEach(row => {
        row.style.display = '';
    });
}
