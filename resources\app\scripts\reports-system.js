// ===============================
// نظام التقارير المتقدمة
// ===============================

class ReportsSystem {
    constructor() {
        this.reportTypes = {
            customers: 'تقرير الزبائن',
            certificates: 'تقرير الشهادات',
            appointments: 'تقرير المواعيد',
            debts: 'تقرير الديون',
            financial: 'التقرير المالي',
            monthly: 'التقرير الشهري',
            yearly: 'التقرير السنوي'
        };
        
        this.init();
    }

    init() {
        this.setupReportButtons();
        this.setupDateFilters();
        this.setupExportOptions();
    }

    // إعداد أزرار التقارير
    setupReportButtons() {
        const reportButtons = document.querySelectorAll('[data-report-type]');
        reportButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const reportType = e.target.getAttribute('data-report-type');
                this.generateReport(reportType);
            });
        });
    }

    // إعداد فلاتر التاريخ
    setupDateFilters() {
        const dateInputs = document.querySelectorAll('.report-date-filter');
        dateInputs.forEach(input => {
            input.addEventListener('change', () => {
                this.updateReportPreview();
            });
        });
    }

    // إعداد خيارات التصدير
    setupExportOptions() {
        const exportButtons = document.querySelectorAll('.export-report-btn');
        exportButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const format = e.target.getAttribute('data-format');
                this.exportReport(format);
            });
        });
    }

    // توليد التقرير
    async generateReport(reportType) {
        try {
            showToast('جاري إنشاء التقرير...', 'info');
            
            const reportData = await this.getReportData(reportType);
            const reportHTML = this.generateReportHTML(reportType, reportData);
            
            this.displayReport(reportHTML);
            showToast('تم إنشاء التقرير بنجاح', 'success');
            
        } catch (error) {
            console.error('خطأ في إنشاء التقرير:', error);
            showToast('خطأ في إنشاء التقرير', 'error');
        }
    }

    // الحصول على بيانات التقرير
    async getReportData(reportType) {
        const dateFrom = document.getElementById('report-date-from')?.value;
        const dateTo = document.getElementById('report-date-to')?.value;
        
        let data = {};
        
        switch (reportType) {
            case 'customers':
                data = this.getCustomersReportData(dateFrom, dateTo);
                break;
            case 'certificates':
                data = this.getCertificatesReportData(dateFrom, dateTo);
                break;
            case 'appointments':
                data = this.getAppointmentsReportData(dateFrom, dateTo);
                break;
            case 'debts':
                data = this.getDebtsReportData(dateFrom, dateTo);
                break;
            case 'financial':
                data = this.getFinancialReportData(dateFrom, dateTo);
                break;
            case 'monthly':
                data = this.getMonthlyReportData();
                break;
            case 'yearly':
                data = this.getYearlyReportData();
                break;
            default:
                throw new Error('نوع تقرير غير مدعوم');
        }
        
        return data;
    }

    // تقرير الزبائن
    getCustomersReportData(dateFrom, dateTo) {
        const customers = appData.customers || [];
        const filteredCustomers = this.filterByDate(customers, dateFrom, dateTo, 'createdAt');
        
        return {
            title: 'تقرير الزبائن',
            totalCustomers: filteredCustomers.length,
            newCustomers: filteredCustomers.filter(c => this.isNewCustomer(c)).length,
            activeCustomers: filteredCustomers.filter(c => this.isActiveCustomer(c)).length,
            customers: filteredCustomers,
            dateRange: { from: dateFrom, to: dateTo }
        };
    }

    // تقرير الشهادات
    getCertificatesReportData(dateFrom, dateTo) {
        const certificates = appData.certificates || [];
        const filteredCertificates = this.filterByDate(certificates, dateFrom, dateTo, 'issueDate');
        
        const certificatesByType = {};
        filteredCertificates.forEach(cert => {
            certificatesByType[cert.type] = (certificatesByType[cert.type] || 0) + 1;
        });
        
        return {
            title: 'تقرير الشهادات',
            totalCertificates: filteredCertificates.length,
            certificatesByType,
            expiringCertificates: this.getExpiringCertificates(filteredCertificates),
            certificates: filteredCertificates,
            dateRange: { from: dateFrom, to: dateTo }
        };
    }

    // تقرير المواعيد
    getAppointmentsReportData(dateFrom, dateTo) {
        const appointments = appData.appointments || [];
        const filteredAppointments = this.filterByDate(appointments, dateFrom, dateTo, 'appointmentDate');
        
        const appointmentsByStatus = {};
        filteredAppointments.forEach(apt => {
            appointmentsByStatus[apt.status] = (appointmentsByStatus[apt.status] || 0) + 1;
        });
        
        return {
            title: 'تقرير المواعيد',
            totalAppointments: filteredAppointments.length,
            appointmentsByStatus,
            completedAppointments: filteredAppointments.filter(a => a.status === 'completed').length,
            pendingAppointments: filteredAppointments.filter(a => a.status === 'scheduled').length,
            appointments: filteredAppointments,
            dateRange: { from: dateFrom, to: dateTo }
        };
    }

    // تقرير الديون
    getDebtsReportData(dateFrom, dateTo) {
        const debts = appData.debts || [];
        const filteredDebts = this.filterByDate(debts, dateFrom, dateTo, 'createdAt');
        
        const totalDebt = filteredDebts.reduce((sum, debt) => sum + (debt.amount || 0), 0);
        const paidDebts = filteredDebts.filter(d => d.status === 'paid');
        const unpaidDebts = filteredDebts.filter(d => d.status === 'unpaid');
        
        return {
            title: 'تقرير الديون',
            totalDebts: filteredDebts.length,
            totalDebtAmount: totalDebt,
            paidDebts: paidDebts.length,
            unpaidDebts: unpaidDebts.length,
            paidAmount: paidDebts.reduce((sum, debt) => sum + (debt.amount || 0), 0),
            unpaidAmount: unpaidDebts.reduce((sum, debt) => sum + (debt.amount || 0), 0),
            debts: filteredDebts,
            dateRange: { from: dateFrom, to: dateTo }
        };
    }

    // التقرير المالي
    getFinancialReportData(dateFrom, dateTo) {
        const sales = appData.sales || [];
        const purchases = appData.purchases || [];
        const debts = appData.debts || [];
        
        const filteredSales = this.filterByDate(sales, dateFrom, dateTo, 'date');
        const filteredPurchases = this.filterByDate(purchases, dateFrom, dateTo, 'date');
        const filteredDebts = this.filterByDate(debts, dateFrom, dateTo, 'createdAt');
        
        const totalRevenue = filteredSales.reduce((sum, sale) => sum + (sale.amount || 0), 0);
        const totalExpenses = filteredPurchases.reduce((sum, purchase) => sum + (purchase.amount || 0), 0);
        const totalDebts = filteredDebts.reduce((sum, debt) => sum + (debt.amount || 0), 0);
        
        return {
            title: 'التقرير المالي',
            totalRevenue,
            totalExpenses,
            netProfit: totalRevenue - totalExpenses,
            totalDebts,
            salesCount: filteredSales.length,
            purchasesCount: filteredPurchases.length,
            dateRange: { from: dateFrom, to: dateTo }
        };
    }

    // توليد HTML للتقرير
    generateReportHTML(reportType, data) {
        const reportDate = new Date().toLocaleDateString('ar-SA');
        
        let html = `
            <div class="report-container">
                <div class="report-header">
                    <h1>${data.title}</h1>
                    <div class="report-info">
                        <p>تاريخ التقرير: ${reportDate}</p>
                        ${data.dateRange ? `<p>الفترة: من ${data.dateRange.from || 'البداية'} إلى ${data.dateRange.to || 'النهاية'}</p>` : ''}
                    </div>
                </div>
                
                <div class="report-summary">
                    ${this.generateSummaryHTML(reportType, data)}
                </div>
                
                <div class="report-details">
                    ${this.generateDetailsHTML(reportType, data)}
                </div>
                
                <div class="report-footer">
                    <p>تم إنشاء هذا التقرير بواسطة نظام CFGPLProgram</p>
                    <p>شركة Future Fuel Corporation</p>
                </div>
            </div>
        `;
        
        return html;
    }

    // توليد ملخص التقرير
    generateSummaryHTML(reportType, data) {
        switch (reportType) {
            case 'customers':
                return `
                    <div class="summary-cards">
                        <div class="summary-card">
                            <h3>إجمالي الزبائن</h3>
                            <p class="summary-number">${data.totalCustomers}</p>
                        </div>
                        <div class="summary-card">
                            <h3>زبائن جدد</h3>
                            <p class="summary-number">${data.newCustomers}</p>
                        </div>
                        <div class="summary-card">
                            <h3>زبائن نشطون</h3>
                            <p class="summary-number">${data.activeCustomers}</p>
                        </div>
                    </div>
                `;
            
            case 'financial':
                return `
                    <div class="summary-cards">
                        <div class="summary-card revenue">
                            <h3>إجمالي الإيرادات</h3>
                            <p class="summary-number">${data.totalRevenue.toLocaleString()} دج</p>
                        </div>
                        <div class="summary-card expenses">
                            <h3>إجمالي المصروفات</h3>
                            <p class="summary-number">${data.totalExpenses.toLocaleString()} دج</p>
                        </div>
                        <div class="summary-card profit">
                            <h3>صافي الربح</h3>
                            <p class="summary-number">${data.netProfit.toLocaleString()} دج</p>
                        </div>
                    </div>
                `;
            
            default:
                return '<p>ملخص التقرير غير متوفر</p>';
        }
    }

    // توليد تفاصيل التقرير
    generateDetailsHTML(reportType, data) {
        // سيتم إضافة المزيد من التفاصيل حسب نوع التقرير
        return '<p>تفاصيل التقرير قيد التطوير...</p>';
    }

    // عرض التقرير
    displayReport(reportHTML) {
        const reportModal = document.getElementById('report-modal');
        const reportContent = document.getElementById('report-content');
        
        if (reportModal && reportContent) {
            reportContent.innerHTML = reportHTML;
            reportModal.style.display = 'block';
        }
    }

    // تصدير التقرير
    exportReport(format) {
        const reportContent = document.getElementById('report-content');
        if (!reportContent) return;
        
        switch (format) {
            case 'pdf':
                this.exportToPDF(reportContent);
                break;
            case 'excel':
                this.exportToExcel(reportContent);
                break;
            case 'print':
                this.printReport(reportContent);
                break;
            default:
                showToast('تنسيق التصدير غير مدعوم', 'error');
        }
    }

    // طباعة التقرير
    printReport(content) {
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>تقرير CFGPLProgram</title>
                    <style>
                        body { font-family: Arial, sans-serif; direction: rtl; }
                        .report-container { max-width: 800px; margin: 0 auto; }
                        .summary-cards { display: flex; gap: 20px; margin: 20px 0; }
                        .summary-card { flex: 1; padding: 15px; border: 1px solid #ddd; text-align: center; }
                        .summary-number { font-size: 24px; font-weight: bold; color: #2c3e50; }
                        @media print { body { margin: 0; } }
                    </style>
                </head>
                <body>
                    ${content.innerHTML}
                </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
    }

    // دوال مساعدة
    filterByDate(items, dateFrom, dateTo, dateField) {
        if (!dateFrom && !dateTo) return items;
        
        return items.filter(item => {
            const itemDate = new Date(item[dateField]);
            const from = dateFrom ? new Date(dateFrom) : new Date('1900-01-01');
            const to = dateTo ? new Date(dateTo) : new Date();
            
            return itemDate >= from && itemDate <= to;
        });
    }

    isNewCustomer(customer) {
        const createdDate = new Date(customer.createdAt);
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        return createdDate >= thirtyDaysAgo;
    }

    isActiveCustomer(customer) {
        // تحديد الزبون النشط بناءً على آخر نشاط
        return customer.lastActivity && new Date(customer.lastActivity) > new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
    }

    getExpiringCertificates(certificates) {
        const thirtyDaysFromNow = new Date();
        thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
        
        return certificates.filter(cert => {
            const expiryDate = new Date(cert.expiryDate);
            return expiryDate <= thirtyDaysFromNow;
        });
    }
}

// تهيئة نظام التقارير
let reportsSystem;
document.addEventListener('DOMContentLoaded', () => {
    reportsSystem = new ReportsSystem();
});
