// ===============================
// مراقب الأداء - CFGPLProgram
// ===============================

class PerformanceMonitor {
    constructor() {
        this.metrics = {
            loadTime: 0,
            saveTime: 0,
            renderTime: 0,
            searchTime: 0,
            memoryUsage: 0
        };
        
        this.timers = new Map();
        this.isEnabled = true;
    }

    // بدء قياس الوقت
    startTimer(name) {
        if (!this.isEnabled) return;
        this.timers.set(name, performance.now());
    }

    // إنهاء قياس الوقت
    endTimer(name) {
        if (!this.isEnabled) return;
        
        const startTime = this.timers.get(name);
        if (startTime) {
            const duration = performance.now() - startTime;
            this.metrics[name] = duration;
            this.timers.delete(name);
            
            console.log(`⏱️ ${name}: ${duration.toFixed(2)}ms`);
            return duration;
        }
        return 0;
    }

    // قياس استهلاك الذاكرة
    measureMemory() {
        if (!this.isEnabled) return;
        
        if (performance.memory) {
            const memory = {
                used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
                total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
                limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
            };
            
            this.metrics.memoryUsage = memory.used;
            console.log(`🧠 Memory: ${memory.used}MB / ${memory.total}MB (Limit: ${memory.limit}MB)`);
            
            return memory;
        }
        return null;
    }

    // قياس أداء دالة
    measureFunction(name, func) {
        if (!this.isEnabled) return func();
        
        this.startTimer(name);
        const result = func();
        this.endTimer(name);
        
        return result;
    }

    // قياس أداء دالة async
    async measureAsyncFunction(name, func) {
        if (!this.isEnabled) return await func();
        
        this.startTimer(name);
        const result = await func();
        this.endTimer(name);
        
        return result;
    }

    // الحصول على تقرير الأداء
    getPerformanceReport() {
        const memory = this.measureMemory();
        
        return {
            metrics: { ...this.metrics },
            memory: memory,
            timestamp: new Date().toISOString(),
            recommendations: this.getRecommendations()
        };
    }

    // الحصول على توصيات التحسين
    getRecommendations() {
        const recommendations = [];
        
        if (this.metrics.loadTime > 1000) {
            recommendations.push('تحميل البيانات بطيء - فكر في تحسين الكاش');
        }
        
        if (this.metrics.saveTime > 500) {
            recommendations.push('حفظ البيانات بطيء - فكر في تحسين عملية الحفظ');
        }
        
        if (this.metrics.renderTime > 200) {
            recommendations.push('عرض الجداول بطيء - فكر في Virtual Scrolling');
        }
        
        if (this.metrics.searchTime > 100) {
            recommendations.push('البحث بطيء - فكر في تحسين خوارزمية البحث');
        }
        
        if (this.metrics.memoryUsage > 100) {
            recommendations.push('استهلاك ذاكرة عالي - فكر في تنظيف الذاكرة');
        }
        
        return recommendations;
    }

    // عرض تقرير الأداء في وحدة التحكم
    logPerformanceReport() {
        const report = this.getPerformanceReport();
        
        console.group('📊 تقرير الأداء');
        console.table(report.metrics);
        
        if (report.memory) {
            console.log('🧠 استهلاك الذاكرة:', report.memory);
        }
        
        if (report.recommendations.length > 0) {
            console.warn('⚠️ توصيات التحسين:');
            report.recommendations.forEach(rec => console.warn(`- ${rec}`));
        }
        
        console.groupEnd();
        
        return report;
    }

    // تفعيل/إلغاء تفعيل المراقب
    toggle(enabled) {
        this.isEnabled = enabled;
        console.log(`📊 مراقب الأداء: ${enabled ? 'مفعل' : 'معطل'}`);
    }

    // إعادة تعيين المقاييس
    reset() {
        this.metrics = {
            loadTime: 0,
            saveTime: 0,
            renderTime: 0,
            searchTime: 0,
            memoryUsage: 0
        };
        this.timers.clear();
        console.log('🔄 تم إعادة تعيين مقاييس الأداء');
    }
}

// إنشاء مثيل مراقب الأداء
const performanceMonitor = new PerformanceMonitor();

// تصدير المراقب للاستخدام العام
window.performanceMonitor = performanceMonitor;

// مراقبة دورية للأداء (كل دقيقة)
setInterval(() => {
    if (performanceMonitor.isEnabled) {
        performanceMonitor.measureMemory();
    }
}, 60000);

// عرض تقرير الأداء كل 5 دقائق
setInterval(() => {
    if (performanceMonitor.isEnabled) {
        performanceMonitor.logPerformanceReport();
    }
}, 5 * 60000);

console.log('📊 تم تحميل مراقب الأداء بنجاح');
