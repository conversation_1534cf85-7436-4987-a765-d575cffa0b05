const { app, BrowserWindow, Menu, ipcMain, dialog } = require('electron');
const path = require('path');
const fs = require('fs');

let mainWindow;

function createWindow() {
    mainWindow = new BrowserWindow({
        width: 1400,
        height: 900,
        minWidth: 1200,
        minHeight: 800,
        icon: path.join(__dirname, 'resources/app/assets/icon.ico'),
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            enableRemoteModule: false,
            allowRunningInsecureContent: false,
            experimentalFeatures: false,
            webSecurity: true,
            preload: path.join(__dirname, 'preload.js'),
            sandbox: false // نحتاج false للوصول إلى APIs
        },
        titleBarStyle: 'default',
        show: false
    });

    // تحميل التطبيق
    mainWindow.loadFile('resources/app/index.html');

    // تعيين Content Security Policy
    mainWindow.webContents.session.webRequest.onHeadersReceived((details, callback) => {
        callback({
            responseHeaders: {
                ...details.responseHeaders,
                'Content-Security-Policy': [
                    "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: https://cdnjs.cloudflare.com; " +
                    "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
                    "style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com; " +
                    "font-src 'self' https://cdnjs.cloudflare.com; " +
                    "img-src 'self' data:; " +
                    "connect-src 'self';"
                ]
            }
        });
    });

    // منع فتح الروابط الخارجية
    mainWindow.webContents.setWindowOpenHandler(({ url }) => {
        // منع فتح نوافذ جديدة
        return { action: 'deny' };
    });

    // منع التنقل إلى مواقع خارجية
    mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
        const parsedUrl = new URL(navigationUrl);

        if (parsedUrl.origin !== 'file://') {
            event.preventDefault();
        }
    });

    // إظهار النافذة عند الانتهاء من التحميل
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();

        // فتح أدوات المطور في وضع التطوير
        if (process.env.NODE_ENV === 'development') {
            mainWindow.webContents.openDevTools();
        }
    });

    // إعداد القائمة
    createMenu();
}

function createMenu() {
    const template = [
        {
            label: 'ملف',
            submenu: [
                {
                    label: 'جديد',
                    accelerator: 'CmdOrCtrl+N',
                    click: () => {
                        mainWindow.webContents.send('menu-new');
                    }
                },
                {
                    label: 'فتح',
                    accelerator: 'CmdOrCtrl+O',
                    click: async () => {
                        const result = await dialog.showOpenDialog(mainWindow, {
                            properties: ['openFile'],
                            filters: [
                                { name: 'JSON Files', extensions: ['json'] }
                            ]
                        });
                        
                        if (!result.canceled) {
                            mainWindow.webContents.send('menu-open', result.filePaths[0]);
                        }
                    }
                },
                {
                    label: 'حفظ',
                    accelerator: 'CmdOrCtrl+S',
                    click: () => {
                        mainWindow.webContents.send('menu-save');
                    }
                },
                { type: 'separator' },
                {
                    label: 'تصدير',
                    accelerator: 'CmdOrCtrl+E',
                    click: async () => {
                        const result = await dialog.showSaveDialog(mainWindow, {
                            defaultPath: `cfgpl-data-${new Date().toISOString().split('T')[0]}.json`,
                            filters: [
                                { name: 'JSON Files', extensions: ['json'] }
                            ]
                        });
                        
                        if (!result.canceled) {
                            mainWindow.webContents.send('menu-export', result.filePath);
                        }
                    }
                },
                { type: 'separator' },
                {
                    label: 'خروج',
                    accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                    click: () => {
                        app.quit();
                    }
                }
            ]
        },
        {
            label: 'تحرير',
            submenu: [
                { label: 'تراجع', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
                { label: 'إعادة', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
                { type: 'separator' },
                { label: 'قص', accelerator: 'CmdOrCtrl+X', role: 'cut' },
                { label: 'نسخ', accelerator: 'CmdOrCtrl+C', role: 'copy' },
                { label: 'لصق', accelerator: 'CmdOrCtrl+V', role: 'paste' }
            ]
        },
        {
            label: 'عرض',
            submenu: [
                { label: 'إعادة تحميل', accelerator: 'CmdOrCtrl+R', role: 'reload' },
                { label: 'إعادة تحميل قسري', accelerator: 'CmdOrCtrl+Shift+R', role: 'forceReload' },
                { label: 'أدوات المطور', accelerator: 'F12', role: 'toggleDevTools' },
                { type: 'separator' },
                { label: 'تكبير', accelerator: 'CmdOrCtrl+Plus', role: 'zoomIn' },
                { label: 'تصغير', accelerator: 'CmdOrCtrl+-', role: 'zoomOut' },
                { label: 'حجم طبيعي', accelerator: 'CmdOrCtrl+0', role: 'resetZoom' },
                { type: 'separator' },
                { label: 'ملء الشاشة', accelerator: 'F11', role: 'togglefullscreen' }
            ]
        },
        {
            label: 'نافذة',
            submenu: [
                { label: 'تصغير', accelerator: 'CmdOrCtrl+M', role: 'minimize' },
                { label: 'إغلاق', accelerator: 'CmdOrCtrl+W', role: 'close' }
            ]
        },
        {
            label: 'مساعدة',
            submenu: [
                {
                    label: 'حول CFGPLProgram',
                    click: () => {
                        dialog.showMessageBox(mainWindow, {
                            type: 'info',
                            title: 'حول CFGPLProgram',
                            message: 'CFGPLProgram v2.2.0',
                            detail: 'نظام إدارة الوقود والتراخيص\nFuture Fuel Corporation\n\nتطوير: فريق التطوير\nالإصدار: 2.2.0'
                        });
                    }
                }
            ]
        }
    ];

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
}

// أحداث التطبيق
app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});

// دوال مساعدة للتحقق من الأمان
function validateDataStructure(data) {
    if (!data || typeof data !== 'object') {
        throw new Error('Invalid data structure');
    }

    // التحقق من الحقول المطلوبة
    const requiredFields = ['gasCards', 'appointments', 'customers', 'suppliers', 'inventory', 'certificates'];
    for (const field of requiredFields) {
        if (!Array.isArray(data[field])) {
            data[field] = [];
        }
    }

    return data;
}

function sanitizeFilePath(filePath) {
    // إزالة المسارات الخطيرة
    return path.normalize(filePath).replace(/^(\.\.[\/\\])+/, '');
}

// معالجة رسائل IPC مع تحسينات أمنية
ipcMain.handle('save-data', async (event, data) => {
    try {
        // التحقق من صحة البيانات
        const validatedData = validateDataStructure(data);

        const dataDir = path.join(app.getPath('userData'), 'data');
        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir, { recursive: true });
        }

        const filePath = path.join(dataDir, 'cfgpl-data.json');

        // إنشاء نسخة احتياطية قبل الحفظ
        if (fs.existsSync(filePath)) {
            const backupPath = path.join(dataDir, `cfgpl-data-backup-${Date.now()}.json`);
            fs.copyFileSync(filePath, backupPath);
        }

        fs.writeFileSync(filePath, JSON.stringify(validatedData, null, 2));
        return { success: true };
    } catch (error) {
        console.error('خطأ في حفظ البيانات:', error);
        return { success: false, error: error.message };
    }
});

ipcMain.handle('load-data', async () => {
    try {
        const filePath = path.join(app.getPath('userData'), 'data', 'cfgpl-data.json');
        if (fs.existsSync(filePath)) {
            const rawData = fs.readFileSync(filePath, 'utf8');

            // التحقق من صحة JSON
            let parsedData;
            try {
                parsedData = JSON.parse(rawData);
            } catch (parseError) {
                throw new Error('ملف البيانات تالف');
            }

            // التحقق من صحة هيكل البيانات
            const validatedData = validateDataStructure(parsedData);

            return { success: true, data: validatedData };
        }
        return { success: false, error: 'لا توجد بيانات محفوظة' };
    } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
        return { success: false, error: error.message };
    }
});

ipcMain.handle('export-data', async (event, filePath, data) => {
    try {
        // التحقق من صحة مسار الملف
        const sanitizedPath = sanitizeFilePath(filePath);

        // التحقق من أن المسار في مجلد آمن
        const userDataPath = app.getPath('userData');
        const documentsPath = app.getPath('documents');
        const desktopPath = app.getPath('desktop');

        const allowedPaths = [userDataPath, documentsPath, desktopPath];
        const isPathAllowed = allowedPaths.some(allowedPath =>
            sanitizedPath.startsWith(allowedPath)
        );

        if (!isPathAllowed) {
            throw new Error('مسار الملف غير مسموح');
        }

        // التحقق من صحة البيانات
        const validatedData = validateDataStructure(data);

        // إضافة معلومات التصدير
        const exportData = {
            exportDate: new Date().toISOString(),
            version: '2.2.0',
            data: validatedData
        };

        fs.writeFileSync(sanitizedPath, JSON.stringify(exportData, null, 2));
        return { success: true };
    } catch (error) {
        console.error('خطأ في تصدير البيانات:', error);
        return { success: false, error: error.message };
    }
});

ipcMain.handle('import-data', async (event, filePath) => {
    try {
        // التحقق من صحة مسار الملف
        const sanitizedPath = sanitizeFilePath(filePath);

        // التحقق من وجود الملف
        if (!fs.existsSync(sanitizedPath)) {
            throw new Error('الملف غير موجود');
        }

        // التحقق من حجم الملف (حد أقصى 50MB)
        const stats = fs.statSync(sanitizedPath);
        if (stats.size > 50 * 1024 * 1024) {
            throw new Error('حجم الملف كبير جداً');
        }

        const rawData = fs.readFileSync(sanitizedPath, 'utf8');

        // التحقق من صحة JSON
        let parsedData;
        try {
            parsedData = JSON.parse(rawData);
        } catch (parseError) {
            throw new Error('ملف البيانات تالف أو غير صحيح');
        }

        // التحقق من تنسيق البيانات المصدرة
        let dataToImport;
        if (parsedData.data && parsedData.version) {
            // ملف مصدر من التطبيق
            dataToImport = parsedData.data;
        } else {
            // ملف بيانات مباشر
            dataToImport = parsedData;
        }

        // التحقق من صحة هيكل البيانات
        const validatedData = validateDataStructure(dataToImport);

        return { success: true, data: validatedData };
    } catch (error) {
        console.error('خطأ في استيراد البيانات:', error);
        return { success: false, error: error.message };
    }
});

ipcMain.handle('create-backup', async (event, data) => {
    try {
        const backupDir = path.join(app.getPath('userData'), 'backups');
        if (!fs.existsSync(backupDir)) {
            fs.mkdirSync(backupDir, { recursive: true });
        }
        
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupPath = path.join(backupDir, `backup-${timestamp}.json`);
        
        fs.writeFileSync(backupPath, JSON.stringify(data, null, 2));
        return { success: true, path: backupPath };
    } catch (error) {
        console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
        return { success: false, error: error.message };
    }
});

ipcMain.handle('get-backups-list', async () => {
    try {
        const backupDir = path.join(app.getPath('userData'), 'backups');
        if (!fs.existsSync(backupDir)) {
            return { success: true, backups: [] };
        }
        
        const files = fs.readdirSync(backupDir)
            .filter(file => file.endsWith('.json'))
            .map(file => {
                const filePath = path.join(backupDir, file);
                const stats = fs.statSync(filePath);
                return {
                    name: file,
                    path: filePath,
                    date: stats.mtime,
                    size: stats.size
                };
            })
            .sort((a, b) => b.date - a.date);
        
        return { success: true, backups: files };
    } catch (error) {
        console.error('خطأ في قراءة قائمة النسخ الاحتياطية:', error);
        return { success: false, error: error.message };
    }
});

ipcMain.handle('restore-backup', async (event, backupPath) => {
    try {
        const data = fs.readFileSync(backupPath, 'utf8');
        return { success: true, data: JSON.parse(data) };
    } catch (error) {
        console.error('خطأ في استعادة النسخة الاحتياطية:', error);
        return { success: false, error: error.message };
    }
});
