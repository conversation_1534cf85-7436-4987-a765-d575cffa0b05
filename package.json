{"name": "cfgplprogram", "version": "2.2.0", "description": "Future Fuel Corporation Management System", "main": "main.js", "scripts": {"start": "electron .", "build": "electron-builder", "dist": "electron-builder --publish=never"}, "keywords": ["gas", "fuel", "management", "certificates", "algeria"], "author": "Future Fuel Corporation", "license": "MIT", "devDependencies": {"electron": "^32.0.0", "electron-builder": "^25.0.0"}, "build": {"appId": "com.futurefuel.cfgplprogram", "productName": "CFGPLProgram", "directories": {"output": "dist"}, "files": ["main.js", "preload.js", "resources/**/*"], "win": {"target": "nsis", "icon": "resources/app/assets/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}}