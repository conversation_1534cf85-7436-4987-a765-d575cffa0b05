// ===============================
// نظام الإشعارات المتقدم
// ===============================

class NotificationsSystem {
    constructor() {
        this.notifications = [];
        this.settings = {
            enabled: true,
            sound: true,
            desktop: true,
            autoCheck: true,
            checkInterval: 60000 // دقيقة واحدة
        };
        
        this.init();
    }

    init() {
        this.loadSettings();
        this.setupNotificationPermissions();
        this.startAutoCheck();
        this.setupNotificationCenter();
    }

    // تحميل إعدادات الإشعارات
    loadSettings() {
        const savedSettings = localStorage.getItem('notificationSettings');
        if (savedSettings) {
            this.settings = { ...this.settings, ...JSON.parse(savedSettings) };
        }
    }

    // حفظ إعدادات الإشعارات
    saveSettings() {
        localStorage.setItem('notificationSettings', JSON.stringify(this.settings));
    }

    // إعداد صلاحيات الإشعارات
    async setupNotificationPermissions() {
        if ('Notification' in window && this.settings.desktop) {
            if (Notification.permission === 'default') {
                await Notification.requestPermission();
            }
        }
    }

    // بدء الفحص التلقائي
    startAutoCheck() {
        if (this.settings.autoCheck) {
            setInterval(() => {
                this.checkForNotifications();
            }, this.settings.checkInterval);
            
            // فحص فوري عند البدء
            this.checkForNotifications();
        }
    }

    // فحص الإشعارات
    checkForNotifications() {
        if (!this.settings.enabled) return;

        this.checkCertificateExpirations();
        this.checkAppointmentReminders();
        this.checkOverdueDebts();
        this.checkLowInventory();
        this.checkTransmissionDeadline();
    }

    // فحص انتهاء صلاحية الشهادات
    checkCertificateExpirations() {
        const certificates = appData.certificates || [];
        const thirtyDaysFromNow = new Date();
        thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);

        certificates.forEach(cert => {
            if (cert.nextMonitoring) {
                const expiryDate = new Date(cert.nextMonitoring);
                const daysUntilExpiry = Math.ceil((expiryDate - new Date()) / (1000 * 60 * 60 * 24));

                if (daysUntilExpiry <= 30 && daysUntilExpiry > 0) {
                    this.createNotification({
                        id: `cert-expiry-${cert.id}`,
                        type: 'warning',
                        title: 'تنبيه انتهاء صلاحية شهادة',
                        message: `شهادة ${cert.customerName} (${cert.plateNumber}) ستنتهي خلال ${daysUntilExpiry} يوم`,
                        action: () => this.showSection('certificates'),
                        priority: daysUntilExpiry <= 7 ? 'high' : 'medium'
                    });
                } else if (daysUntilExpiry <= 0) {
                    this.createNotification({
                        id: `cert-expired-${cert.id}`,
                        type: 'error',
                        title: 'شهادة منتهية الصلاحية',
                        message: `شهادة ${cert.customerName} (${cert.plateNumber}) منتهية الصلاحية`,
                        action: () => this.showSection('certificates'),
                        priority: 'high'
                    });
                }
            }
        });
    }

    // فحص تذكيرات المواعيد
    checkAppointmentReminders() {
        const appointments = appData.appointments || [];
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(0, 0, 0, 0);

        const nextDay = new Date(tomorrow);
        nextDay.setDate(nextDay.getDate() + 1);

        appointments.forEach(appointment => {
            if (appointment.status === 'scheduled') {
                const appointmentDate = new Date(appointment.appointmentDate);
                appointmentDate.setHours(0, 0, 0, 0);

                if (appointmentDate.getTime() === tomorrow.getTime()) {
                    this.createNotification({
                        id: `appointment-${appointment.id}`,
                        type: 'info',
                        title: 'تذكير موعد غداً',
                        message: `موعد مع ${appointment.customerName} غداً في ${appointment.appointmentTime}`,
                        action: () => this.showSection('appointments'),
                        priority: 'medium'
                    });
                }
            }
        });
    }

    // فحص الديون المتأخرة
    checkOverdueDebts() {
        const debts = appData.debts || [];
        const overdueDebts = debts.filter(debt => {
            if (debt.status === 'paid') return false;
            
            const dueDate = new Date(debt.dueDate);
            return dueDate < new Date();
        });

        if (overdueDebts.length > 0) {
            const totalOverdue = overdueDebts.reduce((sum, debt) => sum + (debt.amount || 0), 0);
            
            this.createNotification({
                id: 'overdue-debts',
                type: 'warning',
                title: 'ديون متأخرة',
                message: `يوجد ${overdueDebts.length} دين متأخر بقيمة ${totalOverdue.toLocaleString()} دج`,
                action: () => this.showSection('debts'),
                priority: 'high'
            });
        }
    }

    // فحص المخزون المنخفض
    checkLowInventory() {
        const inventory = appData.inventory || [];
        const lowStockItems = inventory.filter(item => 
            item.quantity <= (item.minQuantity || 10)
        );

        if (lowStockItems.length > 0) {
            this.createNotification({
                id: 'low-inventory',
                type: 'warning',
                title: 'مخزون منخفض',
                message: `${lowStockItems.length} منتج بحاجة إلى إعادة تموين`,
                action: () => this.showSection('inventory'),
                priority: 'medium'
            });
        }
    }

    // فحص موعد الإرسال (كل 6 أشهر)
    checkTransmissionDeadline() {
        const settings = appData.settings || {};
        const lastSent = settings.lastTransmissionSent;
        
        if (!lastSent) return;

        const lastSentDate = new Date(lastSent);
        const sixMonthsLater = new Date(lastSentDate);
        sixMonthsLater.setMonth(sixMonthsLater.getMonth() + 6);
        
        const daysUntilDeadline = Math.ceil((sixMonthsLater - new Date()) / (1000 * 60 * 60 * 24));

        if (daysUntilDeadline <= 30 && daysUntilDeadline > 0) {
            this.createNotification({
                id: 'transmission-deadline',
                type: 'info',
                title: 'موعد الإرسال قريب',
                message: `يجب إرسال جدول الإرسال خلال ${daysUntilDeadline} يوم`,
                action: () => this.showSection('transmission-table'),
                priority: 'medium'
            });
        } else if (daysUntilDeadline <= 0) {
            this.createNotification({
                id: 'transmission-overdue',
                type: 'error',
                title: 'موعد الإرسال متأخر',
                message: 'يجب إرسال جدول الإرسال فوراً',
                action: () => this.showSection('transmission-table'),
                priority: 'high'
            });
        }
    }

    // إنشاء إشعار جديد
    createNotification(notification) {
        // تجنب الإشعارات المكررة
        const existingIndex = this.notifications.findIndex(n => n.id === notification.id);
        if (existingIndex !== -1) {
            this.notifications[existingIndex] = { ...notification, timestamp: new Date() };
        } else {
            this.notifications.unshift({ ...notification, timestamp: new Date() });
        }

        // الحد الأقصى للإشعارات
        if (this.notifications.length > 50) {
            this.notifications = this.notifications.slice(0, 50);
        }

        this.updateNotificationBadge();
        this.showToastNotification(notification);
        this.showDesktopNotification(notification);
        this.playNotificationSound(notification);
    }

    // عرض إشعار Toast
    showToastNotification(notification) {
        const typeMap = {
            'info': 'info',
            'warning': 'warning',
            'error': 'error',
            'success': 'success'
        };

        showToast(notification.message, typeMap[notification.type] || 'info');
    }

    // عرض إشعار سطح المكتب
    showDesktopNotification(notification) {
        if (!this.settings.desktop || !('Notification' in window) || Notification.permission !== 'granted') {
            return;
        }

        const desktopNotification = new Notification(notification.title, {
            body: notification.message,
            icon: 'assets/icon.png',
            tag: notification.id
        });

        desktopNotification.onclick = () => {
            window.focus();
            if (notification.action) {
                notification.action();
            }
            desktopNotification.close();
        };

        // إغلاق تلقائي بعد 5 ثوان
        setTimeout(() => {
            desktopNotification.close();
        }, 5000);
    }

    // تشغيل صوت الإشعار
    playNotificationSound(notification) {
        if (!this.settings.sound) return;

        // إنشاء صوت بسيط باستخدام Web Audio API
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            // تردد مختلف حسب نوع الإشعار
            const frequencies = {
                'info': 800,
                'warning': 600,
                'error': 400,
                'success': 1000
            };

            oscillator.frequency.setValueAtTime(frequencies[notification.type] || 800, audioContext.currentTime);
            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.3);
        } catch (error) {
            console.log('لا يمكن تشغيل صوت الإشعار:', error);
        }
    }

    // تحديث شارة الإشعارات
    updateNotificationBadge() {
        const badge = document.getElementById('notification-badge');
        const unreadCount = this.notifications.filter(n => !n.read).length;

        if (badge) {
            if (unreadCount > 0) {
                badge.textContent = unreadCount > 99 ? '99+' : unreadCount;
                badge.style.display = 'block';
            } else {
                badge.style.display = 'none';
            }
        }
    }

    // إعداد مركز الإشعارات
    setupNotificationCenter() {
        const notificationBtn = document.getElementById('notification-btn');
        const notificationPanel = document.getElementById('notification-panel');

        if (notificationBtn) {
            notificationBtn.addEventListener('click', () => {
                this.toggleNotificationPanel();
            });
        }

        // إغلاق اللوحة عند النقر خارجها
        document.addEventListener('click', (e) => {
            if (notificationPanel && !notificationPanel.contains(e.target) && !notificationBtn.contains(e.target)) {
                notificationPanel.style.display = 'none';
            }
        });
    }

    // تبديل لوحة الإشعارات
    toggleNotificationPanel() {
        const panel = document.getElementById('notification-panel');
        if (!panel) return;

        if (panel.style.display === 'block') {
            panel.style.display = 'none';
        } else {
            this.renderNotificationPanel();
            panel.style.display = 'block';
        }
    }

    // عرض لوحة الإشعارات
    renderNotificationPanel() {
        const panel = document.getElementById('notification-panel');
        if (!panel) return;

        const unreadNotifications = this.notifications.filter(n => !n.read);
        const readNotifications = this.notifications.filter(n => n.read);

        let html = `
            <div class="notification-header">
                <h3>الإشعارات</h3>
                <button onclick="notificationsSystem.markAllAsRead()" class="btn btn-sm">
                    تحديد الكل كمقروء
                </button>
            </div>
        `;

        if (this.notifications.length === 0) {
            html += '<div class="notification-empty">لا توجد إشعارات</div>';
        } else {
            if (unreadNotifications.length > 0) {
                html += '<div class="notification-section"><h4>جديد</h4>';
                unreadNotifications.forEach(notification => {
                    html += this.renderNotificationItem(notification);
                });
                html += '</div>';
            }

            if (readNotifications.length > 0) {
                html += '<div class="notification-section"><h4>سابق</h4>';
                readNotifications.slice(0, 10).forEach(notification => {
                    html += this.renderNotificationItem(notification);
                });
                html += '</div>';
            }
        }

        panel.innerHTML = html;
    }

    // عرض عنصر إشعار
    renderNotificationItem(notification) {
        const timeAgo = this.getTimeAgo(notification.timestamp);
        const priorityClass = notification.priority === 'high' ? 'high-priority' : '';
        
        return `
            <div class="notification-item ${notification.read ? 'read' : 'unread'} ${priorityClass}" 
                 onclick="notificationsSystem.handleNotificationClick('${notification.id}')">
                <div class="notification-icon ${notification.type}">
                    <i class="fas ${this.getNotificationIcon(notification.type)}"></i>
                </div>
                <div class="notification-content">
                    <div class="notification-title">${notification.title}</div>
                    <div class="notification-message">${notification.message}</div>
                    <div class="notification-time">${timeAgo}</div>
                </div>
                <button onclick="notificationsSystem.dismissNotification('${notification.id}', event)" 
                        class="notification-dismiss">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
    }

    // الحصول على أيقونة الإشعار
    getNotificationIcon(type) {
        const icons = {
            'info': 'fa-info-circle',
            'warning': 'fa-exclamation-triangle',
            'error': 'fa-exclamation-circle',
            'success': 'fa-check-circle'
        };
        return icons[type] || 'fa-bell';
    }

    // حساب الوقت المنقضي
    getTimeAgo(timestamp) {
        const now = new Date();
        const diff = now - timestamp;
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);

        if (days > 0) return `منذ ${days} يوم`;
        if (hours > 0) return `منذ ${hours} ساعة`;
        if (minutes > 0) return `منذ ${minutes} دقيقة`;
        return 'الآن';
    }

    // التعامل مع النقر على الإشعار
    handleNotificationClick(notificationId) {
        const notification = this.notifications.find(n => n.id === notificationId);
        if (!notification) return;

        // تحديد كمقروء
        notification.read = true;
        this.updateNotificationBadge();

        // تنفيذ الإجراء
        if (notification.action) {
            notification.action();
        }

        // إغلاق اللوحة
        const panel = document.getElementById('notification-panel');
        if (panel) panel.style.display = 'none';
    }

    // إزالة إشعار
    dismissNotification(notificationId, event) {
        if (event) event.stopPropagation();
        
        this.notifications = this.notifications.filter(n => n.id !== notificationId);
        this.updateNotificationBadge();
        this.renderNotificationPanel();
    }

    // تحديد جميع الإشعارات كمقروءة
    markAllAsRead() {
        this.notifications.forEach(n => n.read = true);
        this.updateNotificationBadge();
        this.renderNotificationPanel();
    }

    // عرض قسم معين
    showSection(sectionId) {
        const section = document.getElementById(sectionId);
        if (section) {
            // إخفاء جميع الأقسام
            document.querySelectorAll('main section').forEach(s => s.classList.remove('active-section'));
            
            // إظهار القسم المطلوب
            section.classList.add('active-section');
            
            // تحديث التنقل
            document.querySelectorAll('nav a').forEach(a => a.classList.remove('active'));
            const navLink = document.querySelector(`nav a[data-section="${sectionId}"]`);
            if (navLink) navLink.classList.add('active');
        }
    }

    // تحديث إعدادات الإشعارات
    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        this.saveSettings();
        
        if (newSettings.autoCheck !== undefined) {
            if (newSettings.autoCheck) {
                this.startAutoCheck();
            }
        }
    }
}

// تهيئة نظام الإشعارات
let notificationsSystem;
document.addEventListener('DOMContentLoaded', () => {
    notificationsSystem = new NotificationsSystem();
});
