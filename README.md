# CFGPLProgram - Electron Desktop Application

## 🚀 نظام إدارة الوقود والتراخيص - تطبيق سطح المكتب

### 📋 نظرة عامة
تطبيق سطح مكتب متكامل مبني بتقنية Electron لإدارة الوقود والتراخيص في شركة Future Fuel Corporation.

### ✨ الميزات الرئيسية

#### 🏠 **لوحة التحكم**
- إحصائيات شاملة ومباشرة
- تنبيهات ذكية للمواعيد والتجديدات
- مراقبة الديون المستحقة

#### 👥 **إدارة الزبائن**
- نظام بحث ذكي متقدم
- نماذج شاملة لبيانات الزبائن والمركبات
- إدارة معلومات الخزانات

#### 📜 **نظام الشهادات المتقدم**
- **5 أنواع شهادات**:
  - شهادة تركيب (Installation)
  - شهادة ختم (Étanchéité)
  - شهادة ضمان (Garantie)
  - شهادة نزع الغاز (Dépose)
  - شهادة مراقبة (Contrôle)
- طباعة احترافية بتصميم رسمي
- تتبع تواريخ الانتهاء والتذكيرات

#### 📅 **إدارة المواعيد**
- تقويم تفاعلي
- جدولة المواعيد
- تذكيرات تلقائية

#### 💾 **إدارة البيانات المتقدمة**
- حفظ تلقائي في ملفات النظام
- نسخ احتياطية متعددة
- تصدير/استيراد البيانات
- استعادة النسخ الاحتياطية

### 🛠️ المتطلبات

#### **الأساسية:**
- **Node.js** (الإصدار 16 أو أحدث)
- **npm** (يأتي مع Node.js)
- **نظام التشغيل**: Windows, macOS, Linux

#### **للتطوير:**
- **Git** (اختياري)
- **محرر نصوص** (VS Code مستحسن)

### 📦 التثبيت والتشغيل

#### **الطريقة الأولى: التثبيت التلقائي**
```bash
# 1. انقر نقراً مزدوجاً على
install.bat

# 2. انتظر انتهاء التثبيت

# 3. انقر نقراً مزدوجاً على
start.bat
```

#### **الطريقة الثانية: التثبيت اليدوي**
```bash
# 1. فتح Terminal/Command Prompt في مجلد المشروع

# 2. تثبيت التبعيات
npm install

# 3. تشغيل التطبيق
npm start
```

#### **الطريقة الثالثة: بناء ملف تنفيذي**
```bash
# بناء التطبيق للتوزيع
npm run build

# الملف التنفيذي سيكون في مجلد dist/
```

### 🎯 كيفية الاستخدام

#### **إضافة زبون جديد:**
1. اذهب إلى قسم "الزبائن"
2. انقر "إضافة زبون جديد"
3. املأ جميع البيانات المطلوبة
4. اختر نوع الشهادة المطلوبة
5. احفظ البيانات

#### **طباعة شهادة:**
1. ابحث عن الزبون أو أضف زبون جديد
2. اختر نوع الشهادة من القائمة
3. انقر "طباعة الشهادة"
4. ستفتح نافذة طباعة جديدة

#### **إدارة النسخ الاحتياطية:**
1. اذهب إلى قسم "الإعدادات"
2. انقر "إنشاء نسخة احتياطية"
3. لاستعادة نسخة: اختر من القائمة وانقر "استعادة"

### 📁 هيكل المشروع

```
CFGPLProgram_Electron/
├── 📄 package.json              # إعدادات المشروع
├── 📄 main.js                   # الملف الرئيسي لـ Electron
├── 📄 preload.js                # ملف الأمان
├── 📄 install.bat               # ملف التثبيت
├── 📄 start.bat                 # ملف التشغيل
└── 📁 resources/                # موارد التطبيق
    └── 📁 app/                  # التطبيق الأساسي
        ├── 📄 index.html        # الواجهة الرئيسية
        ├── 📁 scripts/          # ملفات JavaScript
        │   └── 📄 script.js     # المنطق الرئيسي
        ├── 📁 styles/           # ملفات CSS
        │   └── 📄 styles.css    # التنسيقات
        └── 📁 assets/           # الصور والأيقونات
            └── 📄 favicon.ico   # أيقونة التطبيق
```

### 🔧 الميزات التقنية

#### **تقنيات مستخدمة:**
- **Electron**: إطار عمل التطبيق
- **HTML5/CSS3**: الواجهة الأمامية
- **JavaScript ES6+**: المنطق البرمجي
- **Font Awesome**: الأيقونات
- **Node.js APIs**: إدارة الملفات

#### **الأمان:**
- **Context Isolation**: عزل السياق
- **Preload Scripts**: نصوص آمنة
- **No Node Integration**: عدم تكامل Node مباشر

#### **الأداء:**
- **تحميل سريع**: أقل من 3 ثوان
- **استجابة فورية**: واجهة متجاوبة
- **ذاكرة محسنة**: استهلاك قليل

### 💾 إدارة البيانات

#### **التخزين:**
- **ملفات محلية**: في مجلد userData
- **تشفير**: حماية البيانات الحساسة
- **نسخ احتياطية**: تلقائية ويدوية

#### **التصدير/الاستيراد:**
- **تنسيق JSON**: سهولة النقل
- **ضغط البيانات**: توفير المساحة
- **التحقق من الصحة**: منع الأخطاء

### 🎨 التخصيص

#### **الألوان والثيمات:**
- **الوضع المظلم**: تبديل سهل
- **ألوان قابلة للتخصيص**: في ملف CSS
- **خطوط عربية**: دعم كامل

#### **الإعدادات:**
- **اسم الشركة**: قابل للتغيير
- **عنوان الشركة**: قابل للتغيير
- **اسم الخبير**: للشهادات
- **مكان الإصدار**: للشهادات

### 🐛 استكشاف الأخطاء

#### **مشاكل شائعة:**

**1. التطبيق لا يبدأ:**
```bash
# تحقق من تثبيت Node.js
node --version

# إعادة تثبيت التبعيات
npm install
```

**2. خطأ في حفظ البيانات:**
- تحقق من صلاحيات الكتابة
- تأكد من وجود مساحة كافية

**3. مشاكل الطباعة:**
- تحقق من إعدادات المتصفح
- تأكد من وجود طابعة

### 📞 الدعم والمساعدة

#### **للمساعدة التقنية:**
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +213 XXX XXX XXX
- **الموقع**: www.futurefuel.com

#### **التحديثات:**
- **تحديثات تلقائية**: قريباً
- **إشعارات**: عند توفر إصدار جديد
- **ملاحظات الإصدار**: في كل تحديث

### 📄 الترخيص

هذا التطبيق مملوك لشركة **Future Fuel Corporation** ومحمي بحقوق الطبع والنشر.

---

**Future Fuel Corporation**  
*نحو مستقبل أفضل للطاقة*

**الإصدار**: 2.2.0  
**تاريخ الإصدار**: 2024  
**المطور**: فريق التطوير - Future Fuel Corporation
