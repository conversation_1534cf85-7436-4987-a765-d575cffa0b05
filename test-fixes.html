<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الإصلاحات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        #results {
            margin-top: 20px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>اختبار إصلاحات النظام</h1>
    
    <div class="test-section">
        <h2>اختبار حفظ الزبائن</h2>
        <button class="btn" onclick="testCustomerSave()">اختبار حفظ زبون</button>
        <button class="btn" onclick="testCustomerForm()">اختبار نموذج الزبون</button>
    </div>
    
    <div class="test-section">
        <h2>اختبار المواعيد</h2>
        <button class="btn" onclick="testAppointmentAdd()">اختبار إضافة موعد</button>
        <button class="btn" onclick="testAppointmentForm()">اختبار نموذج الموعد</button>
    </div>
    
    <div class="test-section">
        <h2>اختبار الإشعارات</h2>
        <button class="btn" onclick="testNotifications()">اختبار الإشعارات</button>
        <button class="btn" onclick="testNotificationSettings()">اختبار إعدادات الإشعارات</button>
    </div>
    
    <div id="results"></div>

    <script>
        // محاكاة البيانات
        let appData = {
            customers: [],
            appointments: [],
            settings: {}
        };

        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = type;
            div.textContent = new Date().toLocaleTimeString() + ': ' + message;
            results.appendChild(div);
            console.log(message);
        }

        function generateId() {
            return 'test_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }

        function testCustomerSave() {
            log('بدء اختبار حفظ الزبون...');
            
            try {
                const customerData = {
                    id: generateId(),
                    name: 'زبون تجريبي',
                    phone: '0555123456',
                    address: 'عنوان تجريبي',
                    debt: 0,
                    registrationDate: new Date().toISOString().split('T')[0]
                };

                if (!appData.customers) {
                    appData.customers = [];
                }

                appData.customers.push(customerData);
                log('تم حفظ الزبون بنجاح: ' + customerData.name, 'success');
                log('عدد الزبائن الحالي: ' + appData.customers.length, 'success');
                
            } catch (error) {
                log('خطأ في حفظ الزبون: ' + error.message, 'error');
            }
        }

        function testCustomerForm() {
            log('اختبار نموذج الزبون...');
            
            // إنشاء نموذج تجريبي
            const form = document.createElement('form');
            form.id = 'test-customer-form';
            form.innerHTML = `
                <input type="text" id="test-customer-name" value="زبون تجريبي" required>
                <input type="tel" id="test-customer-phone" value="0555123456" required>
                <input type="text" id="test-customer-address" value="عنوان تجريبي" required>
                <input type="number" id="test-customer-debt" value="0">
                <button type="submit">حفظ</button>
            `;
            
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                log('تم إرسال النموذج بنجاح', 'success');
                
                const formData = {
                    name: document.getElementById('test-customer-name').value,
                    phone: document.getElementById('test-customer-phone').value,
                    address: document.getElementById('test-customer-address').value,
                    debt: document.getElementById('test-customer-debt').value
                };
                
                log('بيانات النموذج: ' + JSON.stringify(formData), 'success');
            });
            
            document.body.appendChild(form);
            
            // محاكاة إرسال النموذج
            setTimeout(() => {
                form.dispatchEvent(new Event('submit'));
                document.body.removeChild(form);
            }, 100);
        }

        function testAppointmentAdd() {
            log('بدء اختبار إضافة موعد...');
            
            try {
                const appointmentData = {
                    id: generateId(),
                    customerName: 'زبون تجريبي',
                    appointmentDate: new Date().toISOString().split('T')[0],
                    appointmentTime: '10:00',
                    serviceType: 'تركيب',
                    status: 'scheduled',
                    notes: 'موعد تجريبي'
                };

                if (!appData.appointments) {
                    appData.appointments = [];
                }

                appData.appointments.push(appointmentData);
                log('تم إضافة الموعد بنجاح: ' + appointmentData.customerName, 'success');
                log('عدد المواعيد الحالي: ' + appData.appointments.length, 'success');
                
            } catch (error) {
                log('خطأ في إضافة الموعد: ' + error.message, 'error');
            }
        }

        function testAppointmentForm() {
            log('اختبار نموذج الموعد...');
            
            // محاكاة زر إضافة موعد
            const button = document.createElement('button');
            button.id = 'test-add-appointment-btn';
            button.textContent = 'إضافة موعد';
            
            button.addEventListener('click', (e) => {
                e.preventDefault();
                log('تم النقر على زر إضافة موعد', 'success');
                
                // محاكاة فتح نافذة الموعد
                const appointmentData = {
                    customerName: 'زبون تجريبي',
                    appointmentDate: '2024-01-15',
                    appointmentTime: '14:30',
                    serviceType: 'مراقبة',
                    notes: 'موعد تجريبي من النموذج'
                };
                
                log('بيانات الموعد: ' + JSON.stringify(appointmentData), 'success');
            });
            
            document.body.appendChild(button);
            
            // محاكاة النقر
            setTimeout(() => {
                button.click();
                document.body.removeChild(button);
            }, 100);
        }

        function testNotifications() {
            log('اختبار نظام الإشعارات...');
            
            try {
                // محاكاة نظام الإشعارات
                const mockNotificationSystem = {
                    notifications: [],
                    createNotification: function(notification) {
                        this.notifications.push(notification);
                        log('تم إنشاء إشعار: ' + notification.title, 'success');
                    },
                    updateNotificationBadge: function() {
                        log('تم تحديث شارة الإشعارات', 'success');
                    }
                };
                
                // اختبار إنشاء إشعار
                mockNotificationSystem.createNotification({
                    id: 'test-notification',
                    type: 'info',
                    title: 'إشعار تجريبي',
                    message: 'هذا إشعار تجريبي',
                    priority: 'medium'
                });
                
                log('عدد الإشعارات: ' + mockNotificationSystem.notifications.length, 'success');
                
            } catch (error) {
                log('خطأ في نظام الإشعارات: ' + error.message, 'error');
            }
        }

        function testNotificationSettings() {
            log('اختبار إعدادات الإشعارات...');
            
            try {
                const settings = {
                    enabled: true,
                    sound: true,
                    desktop: false,
                    autoCheck: true,
                    checkInterval: 60000
                };
                
                appData.settings = { ...appData.settings, ...settings };
                log('تم حفظ إعدادات الإشعارات: ' + JSON.stringify(settings), 'success');
                
            } catch (error) {
                log('خطأ في إعدادات الإشعارات: ' + error.message, 'error');
            }
        }

        // تشغيل اختبار أولي
        window.addEventListener('load', () => {
            log('تم تحميل صفحة الاختبار بنجاح', 'success');
        });
    </script>
</body>
</html>
