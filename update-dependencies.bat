@echo off
title CFGPLProgram - Security Update
color 0A

echo.
echo ========================================
echo   CFGPLProgram - Security Update
echo   Future Fuel Corporation
echo ========================================
echo.

echo Updating dependencies for security...
echo.

REM Check if Node.js is installed
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed!
    echo Please install Node.js from https://nodejs.org
    echo.
    pause
    exit /b 1
)

REM Remove old node_modules
echo Removing old dependencies...
if exist "node_modules" (
    rmdir /s /q "node_modules"
)

REM Remove package-lock.json
if exist "package-lock.json" (
    del "package-lock.json"
)

echo Installing updated dependencies...
npm install

if %errorlevel% neq 0 (
    echo.
    echo ERROR: Failed to update dependencies!
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo   Security update completed successfully!
echo ========================================
echo.
echo Security improvements:
echo   - Updated Electron to v32.0.0
echo   - Enhanced Content Security Policy
echo   - Added input validation
echo   - Improved file path security
echo   - Added data structure validation
echo.
pause
