@echo off
title CFGPLProgram - Electron Installation
color 0A

echo.
echo ========================================
echo   CFGPLProgram - Electron Installation
echo   Future Fuel Corporation
echo ========================================
echo.

echo Installing dependencies...
echo.

REM Check if Node.js is installed
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed!
    echo Please install Node.js from https://nodejs.org
    echo.
    pause
    exit /b 1
)

REM Install dependencies
echo Installing Electron and dependencies...
npm install

if %errorlevel% neq 0 (
    echo.
    echo ERROR: Failed to install dependencies!
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo   Installation completed successfully!
echo ========================================
echo.
echo To run the application:
echo   npm start
echo.
echo To build executable:
echo   npm run build
echo.
pause
