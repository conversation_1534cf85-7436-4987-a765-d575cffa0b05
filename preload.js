const { contextBridge, ipcRenderer } = require('electron');

// تعريض واجهة آمنة للتطبيق
contextBridge.exposeInMainWorld('electronAPI', {
    // حفظ وتحميل البيانات
    saveData: (data) => ipcRenderer.invoke('save-data', data),
    loadData: () => ipcRenderer.invoke('load-data'),
    
    // تصدير واستيراد البيانات
    exportData: (filePath, data) => ipcRenderer.invoke('export-data', filePath, data),
    importData: (filePath) => ipcRenderer.invoke('import-data', filePath),
    
    // النسخ الاحتياطية
    createBackup: (data) => ipcRenderer.invoke('create-backup', data),
    getBackupsList: () => ipcRenderer.invoke('get-backups-list'),
    restoreBackup: (backupPath) => ipcRenderer.invoke('restore-backup', backupPath),
    
    // أحداث القائمة
    onMenuNew: (callback) => ipcRenderer.on('menu-new', callback),
    onMenuOpen: (callback) => ipcRenderer.on('menu-open', callback),
    onMenuSave: (callback) => ipcRenderer.on('menu-save', callback),
    onMenuExport: (callback) => ipcRenderer.on('menu-export', callback),
    
    // إزالة المستمعين
    removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel)
});
