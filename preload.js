const { contextBridge, ipcRenderer } = require('electron');

// التحقق من صحة البيانات
function validateData(data) {
    if (typeof data !== 'object' || data === null) {
        throw new Error('Invalid data format');
    }
    return true;
}

// التحقق من صحة مسار الملف
function validateFilePath(filePath) {
    if (typeof filePath !== 'string' || filePath.length === 0) {
        throw new Error('Invalid file path');
    }
    // منع مسارات خطيرة
    if (filePath.includes('..') || filePath.includes('~')) {
        throw new Error('Unsafe file path');
    }
    return true;
}

// تعريض واجهة آمنة للتطبيق
contextBridge.exposeInMainWorld('electronAPI', {
    // حفظ وتحميل البيانات مع التحقق من الأمان
    saveData: (data) => {
        try {
            validateData(data);
            return ipcRenderer.invoke('save-data', data);
        } catch (error) {
            return Promise.reject(error);
        }
    },
    loadData: () => ipcRenderer.invoke('load-data'),

    // تصدير واستيراد البيانات مع التحقق من الأمان
    exportData: (filePath, data) => {
        try {
            validateFilePath(filePath);
            validateData(data);
            return ipcRenderer.invoke('export-data', filePath, data);
        } catch (error) {
            return Promise.reject(error);
        }
    },
    importData: (filePath) => {
        try {
            validateFilePath(filePath);
            return ipcRenderer.invoke('import-data', filePath);
        } catch (error) {
            return Promise.reject(error);
        }
    },
    
    // النسخ الاحتياطية مع التحقق من الأمان
    createBackup: (data) => {
        try {
            validateData(data);
            return ipcRenderer.invoke('create-backup', data);
        } catch (error) {
            return Promise.reject(error);
        }
    },
    getBackupsList: () => ipcRenderer.invoke('get-backups-list'),
    restoreBackup: (backupPath) => {
        try {
            validateFilePath(backupPath);
            return ipcRenderer.invoke('restore-backup', backupPath);
        } catch (error) {
            return Promise.reject(error);
        }
    },
    
    // أحداث القائمة
    onMenuNew: (callback) => ipcRenderer.on('menu-new', callback),
    onMenuOpen: (callback) => ipcRenderer.on('menu-open', callback),
    onMenuSave: (callback) => ipcRenderer.on('menu-save', callback),
    onMenuExport: (callback) => ipcRenderer.on('menu-export', callback),
    
    // إزالة المستمعين
    removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel)
});
