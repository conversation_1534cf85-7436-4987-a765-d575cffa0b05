@echo off
title CFGPLProgram - Electron App
color 0A

echo.
echo ========================================
echo   CFGPLProgram - Electron App
echo   Future Fuel Corporation
echo ========================================
echo.

REM Check if Node.js is installed
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed!
    echo Please install Node.js from https://nodejs.org
    echo.
    pause
    exit /b 1
)

REM Check if node_modules exists
if not exist "node_modules" (
    echo Dependencies not installed. Installing now...
    echo.
    npm install
    if %errorlevel% neq 0 (
        echo.
        echo ERROR: Failed to install dependencies!
        echo Please run install.bat first.
        echo.
        pause
        exit /b 1
    )
)

echo Starting CFGPLProgram...
echo.

npm start

echo.
echo Application closed.
pause
