# 🚀 دليل التثبيت الشامل - CFGPLProgram Electron

## 📋 المتطلبات الأساسية

### ✅ **الخطوة الأولى: تثبيت Node.js**

#### **تحميل Node.js:**
1. اذهب إلى الموقع الرسمي: https://nodejs.org
2. حمل **LTS Version** (الإصدار المستقر)
3. شغل ملف التثبيت واتبع التعليمات
4. أعد تشغيل الكمبيوتر بعد التثبيت

#### **التحقق من التثبيت:**
```bash
# افتح Command Prompt واكتب:
node --version
npm --version

# يجب أن تظهر أرقام الإصدارات
```

## 🛠️ تثبيت CFGPLProgram

### **الطريقة الأولى: التثبيت التلقائي** ⚡
```bash
# 1. انقر نقراً مزدوجاً على:
install.bat

# 2. انتظر انتهاء التثبيت (قد يستغرق 2-5 دقائق)

# 3. عند ظهور "Installation completed successfully!"
# انقر نقراً مزدوجاً على:
start.bat
```

### **الطريقة الثانية: التثبيت اليدوي** 🔧
```bash
# 1. افتح Command Prompt في مجلد المشروع
# (Shift + Right Click > "Open PowerShell window here")

# 2. تثبيت التبعيات
npm install

# 3. تشغيل التطبيق
npm start
```

### **الطريقة الثالثة: بناء ملف تنفيذي** 📦
```bash
# 1. تثبيت التبعيات أولاً
npm install

# 2. بناء التطبيق
npm run build

# 3. الملف التنفيذي سيكون في مجلد:
# dist/CFGPLProgram Setup.exe
```

## 🔧 حل المشاكل الشائعة

### **❌ المشكلة: "node is not recognized"**
**الحل:**
1. تأكد من تثبيت Node.js
2. أعد تشغيل Command Prompt
3. أعد تشغيل الكمبيوتر إذا لزم الأمر

### **❌ المشكلة: "npm install fails"**
**الحل:**
```bash
# امسح مجلد node_modules إذا كان موجوداً
rmdir /s node_modules

# امسح package-lock.json إذا كان موجوداً
del package-lock.json

# أعد التثبيت
npm install
```

### **❌ المشكلة: "Permission denied"**
**الحل:**
1. شغل Command Prompt كـ Administrator
2. أو غير مجلد التثبيت إلى مكان آخر

### **❌ المشكلة: "Application won't start"**
**الحل:**
```bash
# تحقق من الأخطاء
npm start

# إذا ظهرت أخطاء، أعد التثبيت:
npm install
```

## 📁 هيكل الملفات بعد التثبيت

```
CFGPLProgram_Electron/
├── 📄 package.json              ✅ ملف الإعدادات
├── 📄 main.js                   ✅ الملف الرئيسي
├── 📄 preload.js                ✅ ملف الأمان
├── 📄 install.bat               ✅ ملف التثبيت
├── 📄 start.bat                 ✅ ملف التشغيل
├── 📄 README.md                 ✅ دليل المشروع
├── 📄 INSTALLATION_GUIDE.md     ✅ دليل التثبيت
├── 📁 node_modules/             🆕 مجلد التبعيات (بعد npm install)
├── 📄 package-lock.json         🆕 ملف قفل التبعيات
└── 📁 resources/                ✅ موارد التطبيق
    └── 📁 app/                  ✅ التطبيق الأساسي
        ├── 📄 index.html        ✅ الواجهة
        ├── 📁 scripts/          ✅ ملفات JavaScript
        ├── 📁 styles/           ✅ ملفات CSS
        └── 📁 assets/           ✅ الأيقونات
```

## 🎯 التشغيل الناجح

### **علامات النجاح:**
1. ✅ نافذة التطبيق تفتح
2. ✅ الواجهة تظهر بشكل صحيح
3. ✅ القوائم تعمل
4. ✅ يمكن إضافة زبائن
5. ✅ طباعة الشهادات تعمل

### **إذا نجح التشغيل:**
🎉 **مبروك! التطبيق جاهز للاستخدام**

## 📞 الدعم الفني

### **إذا واجهت مشاكل:**
1. **تحقق من المتطلبات**: Node.js مثبت؟
2. **أعد التثبيت**: احذف node_modules وأعد npm install
3. **تحقق من الأخطاء**: شاهد رسائل الخطأ في Command Prompt
4. **اتصل بالدعم**: إذا استمرت المشاكل

### **معلومات مفيدة للدعم:**
- إصدار Windows: `winver`
- إصدار Node.js: `node --version`
- إصدار npm: `npm --version`
- رسائل الخطأ: انسخ النص كاملاً

## 🔄 التحديثات المستقبلية

### **كيفية التحديث:**
1. احتفظ بنسخة احتياطية من البيانات
2. حمل الإصدار الجديد
3. انسخ ملفات البيانات القديمة
4. شغل التطبيق الجديد

### **ملفات البيانات:**
- **Windows**: `%APPDATA%/CFGPLProgram/data/`
- **macOS**: `~/Library/Application Support/CFGPLProgram/data/`
- **Linux**: `~/.config/CFGPLProgram/data/`

---

## 🎊 مرحباً بك في CFGPLProgram!

بعد التثبيت الناجح، ستتمكن من:
- ✅ إدارة بيانات الزبائن
- ✅ طباعة 5 أنواع شهادات
- ✅ إنشاء نسخ احتياطية
- ✅ تصدير/استيراد البيانات
- ✅ استخدام البحث الذكي
- ✅ إدارة المواعيد والتذكيرات

**Future Fuel Corporation**  
*نحو مستقبل أفضل للطاقة*
